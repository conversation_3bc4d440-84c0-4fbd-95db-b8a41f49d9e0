/**
 * User Data Migration Script - Task 1.2.4 Pre-Implementation Fix
 *
 * This script addresses the critical issue found in the performance baseline tests:
 * - `profileCompletion` field undefined (not calculated)
 * - `lastLogin` field undefined (not populated)
 * - Ensuring all calculated fields are properly populated for existing users
 */

import { getPayload } from 'payload'
import configPromise from '../src/payload.config'

// Simple profile completion calculation for migration
function calculateSimpleProfileCompletion(user: any): number {
  let completedCount = 0
  const totalFields = 6 // Core fields

  // Count populated core fields
  if (user.name) completedCount++
  if (user.classification) completedCount++
  if (user.rotaryId) completedCount++
  if (user.rotaryDistrict) completedCount++
  if (user.joiningDate) completedCount++
  if (user.rotaryClub) completedCount++

  return Math.round((completedCount / totalFields) * 100)
}

function isValidRotaryId(rotaryId: string): boolean {
  // Basic validation - can be enhanced with actual Rotary ID format
  return (
    Boolean(rotaryId) &&
    rotaryId.length >= 3 &&
    rotaryId.length <= 20 &&
    /^[A-Za-z0-9\-_\s]+$/.test(rotaryId)
  )
}

async function migrateUserData() {
  console.log('🚀 Starting User Data Migration for Task 1.2.4...')
  console.log('📅 Timestamp:', new Date().toISOString())

  try {
    const payload = await getPayload({ config: configPromise })

    // Step 1: Fetch all users and check their current state
    console.log('\n📊 Step 1: Analyzing current user data...')

    const allUsers = await payload.find({
      collection: 'users',
      limit: 1000, // Adjust if you have more users
      depth: 2,
    })

    console.log(`Found ${allUsers.docs.length} users to analyze`)

    // Step 2: Analyze missing fields
    const issues: {
      missingProfileCompletion: number
      missingLastLogin: number
      usersNeedingUpdate: string[]
    } = {
      missingProfileCompletion: 0,
      missingLastLogin: 0,
      usersNeedingUpdate: [],
    }

    allUsers.docs.forEach(user => {
      let needsUpdate = false

      if (!user.profileCompletion && user.profileCompletion !== 0) {
        issues.missingProfileCompletion++
        needsUpdate = true
      }

      if (!user.lastLogin) {
        issues.missingLastLogin++
        needsUpdate = true
      }

      if (needsUpdate) {
        issues.usersNeedingUpdate.push(user.id)
      }
    })

    console.log('\n📈 Analysis Results:')
    console.log(`- Users missing profileCompletion: ${issues.missingProfileCompletion}`)
    console.log(`- Users missing lastLogin: ${issues.missingLastLogin}`)
    console.log(`- Total users needing updates: ${issues.usersNeedingUpdate.length}`)

    // Step 3: Perform the migration
    console.log('\n🔄 Step 2: Migrating user data...')

    let updated = 0
    let errors = 0

    for (const userId of issues.usersNeedingUpdate) {
      try {
        const user = allUsers.docs.find(u => u.id === userId)
        if (!user) continue

        const updateData: Record<string, any> = {}

        // Calculate profile completion if missing
        if (!user.profileCompletion && user.profileCompletion !== 0) {
          // Simple profile completion calculation based on core fields
          let completedCount = 0
          const totalFields = 6 // Core fields: name, classification, rotaryId, district, joiningDate, club

          if (user.name) completedCount++
          if (user.classification) completedCount++
          if (user.rotaryId) completedCount++
          if (user.rotaryDistrict) completedCount++
          if (user.joiningDate) completedCount++
          if (user.rotaryClub) completedCount++

          updateData.profileCompletion = Math.round((completedCount / totalFields) * 100)
          console.log(`  ✅ Calculated profile completion for ${user.email || user.name || userId}: ${updateData.profileCompletion}%`)
        }

        // Set lastLogin if missing
        if (!user.lastLogin) {
          updateData.lastLogin = user.createdAt || new Date().toISOString()
          console.log(`  ✅ Set lastLogin for ${user.email || user.name || userId}: ${updateData.lastLogin}`)
        }

        // Perform the update
        await payload.update({
          collection: 'users',
          id: userId,
          data: updateData,
        })

        updated++
      } catch (error) {
        console.error(`  ❌ Error updating user ${userId}:`, error)
        errors++
      }
    }

    // Step 4: Verification
    console.log('\n✅ Step 3: Verifying migration results...')

    const verificationResult = await payload.find({
      collection: 'users',
      limit: 5, // Sample a few users
      depth: 1,
    })

    console.log('Sample verification results:')
    verificationResult.docs.forEach(user => {
      console.log(`  - User ${user.email || user.name || user.id}: profileCompletion=${user.profileCompletion}, lastLogin=${user.lastLogin ? '✓' : '✗'}`)
    })

    // Final summary
    console.log('\n🎉 Migration Summary:')
    console.log(`- Users successfully updated: ${updated}`)
    console.log(`- Errors encountered: ${errors}`)
    console.log(`- Migration started: ${new Date().toISOString()}`)
    console.log('- Migration completed successfully! ✅')

    console.log('\n📝 Next Steps:')
    console.log('1. Re-run performance baseline tests')
    console.log('2. Verify API endpoints work with migrated data')
    console.log('3. Proceed with Task 1.2.4 implementation')

  } catch (error) {
    console.error('🚨 Migration failed:', error)
    console.error('\n🔧 Suggested next steps:')
    console.error('1. Check database connection')
    console.error('2. Verify collection configuration')
    console.error('3. Ensure proper permissions')
    console.error('4. Check for locked documents')
    process.exit(1)
  }
}

// Run the migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateUserData()
    .then(() => {
      console.log('\n✨ Migration completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error)
      process.exit(1)
    })
}

export { migrateUserData }
