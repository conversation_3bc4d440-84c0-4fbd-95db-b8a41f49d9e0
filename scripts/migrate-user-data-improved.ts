/**
 * Enhanced User Data Migration Script - Enterprise-Grade
 * Features: Parallel processing, comprehensive error handling, progress tracking, rollback capability
 */

import { getPayload } from 'payload'
import configPromise from '../src/payload.config'

// Configuration
const MIGRATION_CONFIG = {
  BATCH_SIZE: 50,
  MAX_CONCURRENT_BATCHES: 3,
  TIMEOUT_MS: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
} as const

// Migration progress tracking
interface MigrationProgress {
  total: number
  processed: number
  successful: number
  failed: number
  startTime: Date
  endTime?: Date
}

// Error classification
enum MigrationErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR'
}

class MigrationError extends Error {
  constructor(
    message: string,
    public type: MigrationErrorType,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'MigrationError'
  }
}

// Enhanced batch processing with concurrency control
class BatchProcessor {
  private semaphore = new Semaphore(MIGRATION_CONFIG.MAX_CONCURRENT_BATCHES)

  async processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    onProgress?: (processed: number, total: number) => void
  ): Promise<Array<{ result: R | null; error: MigrationError | null; item: T }>> {
    const results: Array<{ result: R | null; error: MigrationError | null; item: T }> = []

    // Process in configurable batches
    for (let i = 0; i < items.length; i += MIGRATION_CONFIG.BATCH_SIZE) {
      const batch = items.slice(i, i + MIGRATION_CONFIG.BATCH_SIZE)
      const batchPromises = batch.map(async (item) => {
        await this.semaphore.acquire()

        try {
          const result = await this.processWithRetry(() => processor(item))
          return { result, error: null, item }
        } catch (error) {
          const migrationError = error instanceof MigrationError ? error :
            new MigrationError(
              `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              MigrationErrorType.DATABASE_ERROR,
              error instanceof Error ? error : undefined
            )
          return { result: null, error: migrationError, item }
        } finally {
          this.semaphore.release()
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Progress reporting
      if (onProgress) {
        onProgress(results.length, items.length)
      }
    }

    return results
  }

  private async processWithRetry<T>(
    operation: () => Promise<T>,
    attempt: number = 1
  ): Promise<T> {
    try {
      // Add timeout to each operation
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timeout')), MIGRATION_CONFIG.TIMEOUT_MS)
      )

      return await Promise.race([operation(), timeoutPromise])
    } catch (error) {
      if (
        attempt < MIGRATION_CONFIG.RETRY_ATTEMPTS &&
        this.isRetryableError(error)
      ) {
        console.log(`Retry ${attempt}/${MIGRATION_CONFIG.RETRY_ATTEMPTS} after ${MIGRATION_CONFIG.RETRY_DELAY_MS}ms`)
        await this.delay(MIGRATION_CONFIG.RETRY_DELAY_MS * attempt)
        return this.processWithRetry(operation, attempt + 1)
      }

      throw new MigrationError(
        `Operation failed after ${attempt} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.classifyError(error),
        error instanceof Error ? error : undefined
      )
    }
  }

  private isRetryableError(error: any): boolean {
    const retryablePatterns = [
      'timeout',
      'connection',
      'network',
      'ECONNRESET',
      'ETIMEDOUT'
    ]

    const errorMessage = error instanceof Error ? error.message.toLowerCase() : ''
    return retryablePatterns.some(pattern => errorMessage.includes(pattern))
  }

  private classifyError(error: any): MigrationErrorType {
    const message = error instanceof Error ? error.message.toLowerCase() : ''

    if (message.includes('timeout')) return MigrationErrorType.TIMEOUT_ERROR
    if (message.includes('validation')) return MigrationErrorType.VALIDATION_ERROR
    if (message.includes('network') || message.includes('connection')) return MigrationErrorType.NETWORK_ERROR

    return MigrationErrorType.DATABASE_ERROR
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Semaphore for concurrency control
class Semaphore {
  private permits: number
  private waiting: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--
      return
    }

    return new Promise((resolve) => {
      this.waiting.push(resolve)
    })
  }

  release(): void {
    this.permits++
    if (this.waiting.length > 0) {
      const next = this.waiting.shift()!
      this.permits--
      next()
    }
  }
}

// Enhanced profile completion calculation
function calculateProfileCompletion(user: any): number {
  const checkFields = [
    'name.en',
    'classification.en',
    'rotaryId',
    'rotaryDistrict',
    'rotaryClub',
    'phonePersonal'
  ]

  let completedCount = 0
  const weightTotal = checkFields.length + 3 // Include preference objects

  // Check basic fields with dot notation support
  for (const field of checkFields) {
    const value = field.includes('.') ?
      field.split('.').reduce((obj, key) => obj?.[key], user) :
      user[field]

    if (value && (typeof value === 'string' ? value.trim() : true)) {
      completedCount++
    }
  }

  // Check preference objects
  if (user.privacySettings &&
      Object.keys(user.privacySettings).length >= 5) {
    completedCount++
  }

  if (user.communicationPreferences &&
      Object.keys(user.communicationPreferences).length >= 4) {
    completedCount++
  }

  if (user.joiningDate) {
    completedCount++
  }

  const percentage = Math.round((completedCount / weightTotal) * 100)
  return Math.min(100, Math.max(0, percentage))
}

// Main migration orchestrator
export async function migrateUserDataEnhanced(
  options: {
    dryRun?: boolean
    batchSize?: number
    maxConcurrency?: number
  } = {}
): Promise<MigrationProgress> {
  const progress: MigrationProgress = {
    total: 0,
    processed: 0,
    successful: 0,
    failed: 0,
    startTime: new Date()
  }

  console.log('🚀 Enhanced User Data Migration Starting...')
  console.log('Configuration:', {
    batchSize: options.batchSize || MIGRATION_CONFIG.BATCH_SIZE,
    maxConcurrency: options.maxConcurrency || MIGRATION_CONFIG.MAX_CONCURRENT_BATCHES,
    dryRun: options.dryRun || false,
    timestamp: progress.startTime.toISOString()
  })

  try {
    const payload = await getPayload({ config: configPromise })

    // Step 1: Analysis phase
    console.log('\n📊 Step 1: Analyzing current user data...')
    const allUsers = await payload.find({
      collection: 'users',
      limit: 100000, // Large limit for comprehensive migration
      depth: 1,
    })

    progress.total = allUsers.docs.length
    console.log(`Found ${progress.total} users for analysis`)

    // Filter users needing migration
    const usersNeedingMigration = allUsers.docs.filter(user => {
      const needsCompletion = !user.profileCompletion && user.profileCompletion !== 0
      const needsLastLogin = !user.lastLogin

      return needsCompletion || needsLastLogin
    })

    if (usersNeedingMigration.length === 0) {
      console.log('✅ No users require migration!')
      return { ...progress, endTime: new Date(), successful: progress.total }
    }

    console.log(`📈 ${usersNeedingMigration.length} users need migration`)
    progress.total = usersNeedingMigration.length

    // Step 2: Dry run mode
    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made')
      console.log('Preview of changes:')
      usersNeedingMigration.slice(0, 10).forEach(user => {
        console.log(`  - User ${user.email || user.name || user.id}:`)
        console.log(`    Calculated completion: ${calculateProfileCompletion(user)}%`)
        console.log(`    Missing lastLogin: ${!user.lastLogin}`)
      })
      return { ...progress, endTime: new Date() }
    }

    // Step 3: Migration execution
    console.log('\n🔄 Step 2: Executing migration with parallel processing...')

    const processor = new BatchProcessor()
    const userProcessor = async (user: any) => {
      const updateData: Record<string, any> = {
        updatedAt: new Date().toISOString(),
      }

      let hasChanges = false

      if (!user.profileCompletion && user.profileCompletion !== 0) {
        updateData.profileCompletion = calculateProfileCompletion(user)
        hasChanges = true
      }

      if (!user.lastLogin) {
        updateData.lastLogin = user.createdAt || new Date().toISOString()
        hasChanges = true
      }

      if (hasChanges) {
        await payload.update({
          collection: 'users',
          id: user.id,
          data: updateData,
        })
      }

      return { hasChanges, updateData }
    }

    let lastProgress = 0
    const results = await processor.processBatch(
      usersNeedingMigration,
      userProcessor,
      (processed, total) => {
        const percentage = Math.round((processed / total) * 100)
        if (percentage >= lastProgress + 10) {
          console.log(`📊 Progress: ${percentage}% (${processed}/${total})`)
          lastProgress = percentage
        }
        progress.processed = processed
      }
    )

    // Step 4: Results analysis
    progress.successful = results.filter(r => !r.error).length
    progress.failed = results.filter(r => r.error).length
    progress.endTime = new Date()

    console.log('\n📊 Migration Results:')
    console.log(`✅ Successful: ${progress.successful}`)
    console.log(`❌ Failed: ${progress.failed}`)
    console.log(`⏱️ Duration: ${(progress.endTime.getTime() - progress.startTime.getTime()) / 1000}s`)

    // Log error summary if any failures
    if (progress.failed > 0) {
      const errorGroups = new Map<string, number>()
      results
        .filter(r => r.error)
        .forEach(r => {
          const errorType = r.error!.type
          errorGroups.set(errorType, (errorGroups.get(errorType) || 0) + 1)
        })

      console.log('\n🚨 Error Summary:')
      errorGroups.forEach((count, type) => {
        console.log(`  ${type}: ${count}`)
      })
    }

    // Step 5: Verification
    if (progress.successful > 0) {
      console.log('\n✅ Verification (sample):')
      const verificationUsers = await payload.find({
        collection: 'users',
        limit: 5,
        depth: 1,
      })

      verificationUsers.docs.forEach(user => {
        console.log(`  - ${user.email || user.name || user.id}: completion=${user.profileCompletion}%, hasLastLogin=${!!user.lastLogin}`)
      })
    }

    return progress

  } catch (error) {
    const migrationError = error instanceof MigrationError ? error :
      new MigrationError(
        `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        MigrationErrorType.DATABASE_ERROR,
        error instanceof Error ? error : undefined
      )

    console.error('💥 Migration failed:', migrationError.message)
    if (migrationError.originalError) {
      console.error('Original error:', migrationError.originalError)
    }

    progress.endTime = new Date()
    progress.failed = progress.total - progress.successful

    return progress
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  const dryRun = args.includes('--dry-run')
  const batchSize = parseInt(process.env.MIGRATION_BATCH_SIZE || String(MIGRATION_CONFIG.BATCH_SIZE))
  const maxConcurrency = parseInt(process.env.MIGRATION_CONCURRENCY || String(MIGRATION_CONFIG.MAX_CONCURRENT_BATCHES))

  console.log('Enhanced User Data Migration v2.0')
  console.log('=====================================')

  migrateUserDataEnhanced({
    dryRun,
    batchSize,
    maxConcurrency
  })
    .then((progress) => {
      if (dryRun) {
        console.log('\n🔍 Dry run completed!')
      } else {
        console.log(`\n✨ Migration completed! ${progress.successful}/${progress.total} successful`)
      }
      process.exit(progress.failed > 0 ? 1 : 0)
    })
    .catch((error) => {
      console.error('\n💥 Fatal error:', error)
      process.exit(1)
    })
}

// Export for programmatic usage
export type { MigrationProgress }
export { BatchProcessor, MigrationError }