import nodemailer from 'nodemailer'
import { registrationConfirmationTemplates, organizerNotificationTemplates, interpolateTemplate } from './emailTemplates'
import type { Payload } from 'payload'

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text: string
}

// Email-specific error types for better error handling
export class EmailError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: unknown
  ) {
    super(message)
    this.name = 'EmailError'
  }
}

export class EmailConnectionError extends EmailError {
  constructor(message: string = 'Failed to establish email connection', originalError?: unknown) {
    super(message, 'CONNECTION_ERROR', originalError)
    this.name = 'EmailConnectionError'
  }
}

export class EmailSendError extends EmailError {
  constructor(message: string = 'Failed to send email', originalError?: unknown) {
    super(message, 'SEND_ERROR', originalError)
    this.name = 'EmailSendError'
  }
}

export class EmailConfigurationError extends EmailError {
  constructor(message: string = 'Email configuration is invalid', originalError?: unknown) {
    super(message, 'CONFIG_ERROR', originalError)
    this.name = 'EmailConfigurationError'
  }
}

// Singleton transporter instance for connection pooling and performance optimization
// This prevents creating new SMTP connections for every email, which would cause:
// - Performance bottlenecks during high traffic
// - Resource exhaustion and server blocking
// - Email delivery failures and delays
let transporter: nodemailer.Transporter | null = null

// Create or reuse transporter with connection pooling
const getTransporter = (): nodemailer.Transporter => {
  if (!transporter) {
    // Validate required environment variables
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      throw new EmailConfigurationError('SMTP_USER and SMTP_PASS environment variables are required')
    }

    if (!process.env.SMTP_HOST) {
      throw new EmailConfigurationError('SMTP_HOST environment variable is required')
    }

    try {
      transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
        // Connection pooling for better performance
        pool: true,
        maxConnections: parseInt(process.env.SMTP_MAX_CONNECTIONS || '5'),
        maxMessages: parseInt(process.env.SMTP_MAX_MESSAGES || '100'),
        rateDelta: parseInt(process.env.SMTP_RATE_DELTA || '1000'),
        rateLimit: parseInt(process.env.SMTP_RATE_LIMIT || '5'),
      })

      // Handle connection events for monitoring
      transporter.on('idle', () => {
        console.log('Email transporter is idle and ready')
      })

      transporter.on('error', (error: unknown) => {
        console.error('Email transporter connection error:', error)
        // Reset transporter on error to allow reconnection
        transporter = null
      })
    } catch (error) {
      throw new EmailConnectionError('Failed to create email transporter', error)
    }
  }

  if (!transporter) {
    throw new EmailConnectionError('Email transporter is not available')
  }

  return transporter
}

// Gracefully close the transporter connection
export const closeTransporter = async (): Promise<void> => {
  if (transporter) {
    try {
      await transporter.close()
      transporter = null
      console.log('Email transporter closed successfully')
    } catch (error) {
      console.error('Error closing email transporter:', error)
    }
  }
}

// Send email function with connection pooling
export const sendEmail = async (options: EmailOptions): Promise<boolean> => {
  // Validate email options
  if (!options.to || !options.subject) {
    throw new EmailSendError('Recipient email and subject are required')
  }

  if (!options.html && !options.text) {
    throw new EmailSendError('Either HTML content or text content must be provided')
  }

  try {
    const transporter = getTransporter()

    const mailOptions = {
      from: `"Rotary Club Tunis Doyen" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    }

    const info = await transporter.sendMail(mailOptions)
    console.log(`Email sent successfully to ${options.to}: ${info.messageId}`)
    return true
  } catch (error) {
    console.error('Error sending email:', error)

    // Determine error type and reset transporter if needed
    if (error instanceof EmailError) {
      throw error
    }

    // Check if it's a connection-related error
    if (error && typeof error === 'object' && 'code' in error) {
      const errorCode = (error as { code: string }).code
      if (['ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT'].includes(errorCode)) {
        transporter = null // Reset transporter for connection errors
        throw new EmailConnectionError(`Connection failed: ${errorCode}`, error)
      }
    }

    // Reset transporter on any error to allow reconnection
    transporter = null
    throw new EmailSendError('Failed to send email', error)
  }
}

// Fetch email template from database
const fetchTemplateFromDB = async (
  payload: Payload,
  type: string,
  language: string
): Promise<EmailTemplate | null> => {
  try {
    const template = await payload.find({
      collection: 'email-templates',
      where: {
        and: [
          {
            type: {
              equals: type,
            },
          },
          {
            language: {
              equals: language,
            },
          },
        ],
      },
      limit: 1,
    })

    if (template.docs && template.docs.length > 0) {
      const doc = template.docs[0]
      return {
        subject: doc.subject,
        html: doc.html,
        text: doc.text,
      }
    }

    return null
  } catch (error) {
    console.error(`Error fetching ${type} template for ${language} from database:`, error)
    return null
  }
}

// Event registration confirmation email template
export const getRegistrationConfirmationTemplate = async (
  payload: Payload,
  eventTitle: string,
  eventDate: string,
  eventLocation: string,
  userName: string,
  language: string = 'en'
): Promise<EmailTemplate> => {
  // Try to fetch template from database
  const dbTemplate = await fetchTemplateFromDB(payload, 'registration_confirmation', language)
  
  if (dbTemplate) {
    return interpolateTemplate(dbTemplate, {
      eventTitle,
      eventDate,
      eventLocation,
      userName
    })
  }

  // Fallback to hardcoded templates
  const languageKey = language as keyof typeof registrationConfirmationTemplates
  const template = registrationConfirmationTemplates[languageKey] || registrationConfirmationTemplates.en

  return interpolateTemplate(template, {
    eventTitle,
    eventDate,
    eventLocation,
    userName
  })
}

// Organizer notification email template
export const getOrganizerNotificationTemplate = async (
  payload: Payload,
  eventTitle: string,
  eventDate: string,
  eventLocation: string,
  attendeeName: string,
  attendeeEmail: string,
  registrationDate: string,
  language: string = 'en'
): Promise<EmailTemplate> => {
  // Try to fetch template from database
  const dbTemplate = await fetchTemplateFromDB(payload, 'organizer_notification', language)
  
  if (dbTemplate) {
    return interpolateTemplate(dbTemplate, {
      eventTitle,
      eventDate,
      eventLocation,
      attendeeName,
      attendeeEmail,
      registrationDate
    })
  }

  // Fallback to hardcoded templates
  const languageKey = language as keyof typeof organizerNotificationTemplates
  const template = organizerNotificationTemplates[languageKey] || organizerNotificationTemplates.en

  return interpolateTemplate(template, {
    eventTitle,
    eventDate,
    eventLocation,
    attendeeName,
    attendeeEmail,
    registrationDate
  })
}