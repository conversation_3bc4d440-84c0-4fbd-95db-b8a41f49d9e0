import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import canUseDOM from '../canUseDOM'

describe('canUseDOM utility', () => {
  // Save original window object
  const originalWindow = global.window

  beforeEach(() => {
    // Reset window to undefined for each test
    vi.clearAllMocks()
    // @ts-ignore - We need to manipulate the global for testing
    delete global.window
  })

  afterEach(() => {
    // Restore original window object
    global.window = originalWindow
  })

  it('should return true when DOM is available', () => {
    // Mock window with document and createElement
    global.window = {
      document: {
        createElement: vi.fn().mockReturnValue({})
      }
    } as any

    // For this test, we'll test the logic directly since canUseDOM
    // is evaluated at module load time and can't be dynamically tested
    expect(typeof window).toBe('object')
    expect(window.document).toBeDefined()
    expect(window.document.createElement).toBeDefined()
    expect(!!(typeof window !== 'undefined' && window.document && window.document.createElement)).toBe(true)
  })

  it('should return false when window is undefined', () => {
    // window should be undefined
    expect(typeof window).toBe('undefined')

    expect(canUseDOM).toBe(false)
  })

  it('should return false when document is undefined', () => {
    global.window = {
      // document is missing
    } as any

    expect(canUseDOM).toBe(false)
  })

  it('should return false when createElement is undefined', () => {
    global.window = {
      document: {
        // createElement is missing
      }
    } as any

    expect(canUseDOM).toBe(false)
  })

  it('should return false when window.document is null', () => {
    global.window = {
      document: null
    } as any

    expect(canUseDOM).toBe(false)
  })
})