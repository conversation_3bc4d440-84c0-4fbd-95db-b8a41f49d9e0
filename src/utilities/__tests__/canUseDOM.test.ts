import { describe, it, expect, vi } from 'vitest'

describe('canUseDOM utility', () => {
  // Test the actual implementation
  it('should return true when running in jsdom test environment', () => {
    // In Vitest with jsdom environment, window should be available
    expect(typeof window).toBe('object')
    expect(window.document).toBeDefined()
    expect(typeof window.document.createElement).toBe('function')
  })

  // Test the logic directly (this is what canUseDOM evaluates)
  it('should evaluate correct boolean for DOM availability', () => {
    const DOMAvailable = !!(typeof window !== 'undefined' && window.document && window.document.createElement)

    // In jsdom test environment, this should be true
    expect(DOMAvailable).toBe(true)
    expect(window).toBeDefined()
    expect(window.document).toBeDefined()
    expect(window.document.createElement).toBeDefined()
  })

  // Test edge cases that would affect the logic
  it('should handle server-side rendering scenario', () => {
    // Simulate what would happen in SSR environment
    const serverSideAvailable = !!(typeof global.window !== 'undefined' && global.window.document && global.window.document.createElement)

    // In Node.js/server environment, this would be false
    // But in jsdom test environment, global.window is available
    expect(serverSideAvailable).toBe(true) // jsdom provides window
  })

  // Test specific scenarios that could break DOM detection
  it('should handle missing createElement function', () => {
    // Backup original createElement
    const originalCreateElement = document.createElement

    // Mock createElement to be undefined
    document.createElement = undefined as any

    // Test the logic with missing createElement
    const DOMAvailable = !!(typeof window !== 'undefined' && window.document && window.document.createElement)

    // Should return false because createElement is falsy
    expect(DOMAvailable).toBe(false)

    // Restore original createElement
    document.createElement = originalCreateElement
  })

  it('should handle missing document property', () => {
    // Test the logic with falsy document
    const DOMAvailable = !!(typeof window !== 'undefined' && null && (null as any)?.createElement)

    // Should return false because document is falsy (null)
    expect(DOMAvailable).toBe(false)
  })
})