// Email templates for Rotary Club Tunis Doyen
// Separated from main service for better maintainability

export const registrationConfirmationTemplates = {
  en: {
    subject: 'Registration Confirmed: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Rotary Club Tunis Doyen</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">Registration Confirmed!</h2>
          <p>Dear {userName},</p>
          <p>Your registration for the following event has been confirmed:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>Date:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>Location:</strong> {eventLocation}</p>
          </div>
          <p>We look forward to seeing you at the event!</p>
          <p>Best regards,<br>Rotary Club Tunis Doyen Team</p>
        </div>
      </div>
    `,
    text: `
      Rotary Club Tunis Doyen - Registration Confirmed!

      Dear {userName},

      Your registration for the following event has been confirmed:

      Event: {eventTitle}
      Date: {eventDate}
      Location: {eventLocation}

      We look forward to seeing you at the event!

      Best regards,
      Rotary Club Tunis Doyen Team
    `
  },
  fr: {
    subject: 'Inscription Confirmée: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Rotary Club Tunis Doyen</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">Inscription Confirmée!</h2>
          <p>Cher {userName},</p>
          <p>Votre inscription pour l'événement suivant a été confirmée:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>Date:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>Lieu:</strong> {eventLocation}</p>
          </div>
          <p>Nous avons hâte de vous voir à l'événement!</p>
          <p>Cordialement,<br>Équipe Rotary Club Tunis Doyen</p>
        </div>
      </div>
    `,
    text: `
      Rotary Club Tunis Doyen - Inscription Confirmée!

      Cher {userName},

      Votre inscription pour l'événement suivant a été confirmée:

      Événement: {eventTitle}
      Date: {eventDate}
      Lieu: {eventLocation}

      Nous avons hâte de vous voir à l'événement!

      Cordialement,
      Équipe Rotary Club Tunis Doyen
    `
  },
  ar: {
    subject: 'تم التأكيد: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">روتاري تونس الدويان</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">تم تأكيد التسجيل!</h2>
          <p>عزيزي {userName}،</p>
          <p>تم تأكيد تسجيلك في الفعالية التالية:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>التاريخ:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>المكان:</strong> {eventLocation}</p>
          </div>
          <p>نتطلع إلى رؤيتك في الفعالية!</p>
          <p>مع خالص التحية،<br>فريق روتاري تونس الدويان</p>
        </div>
      </div>
    `,
    text: `
      روتاري تونس الدويان - تم تأكيد التسجيل!

      عزيزي {userName}،

      تم تأكيد تسجيلك في الفعالية التالية:

      الفعالية: {eventTitle}
      التاريخ: {eventDate}
      المكان: {eventLocation}

      نتطلع إلى رؤيتك في الفعالية!

      مع خالص التحية،
      فريق روتاري تونس الدويان
    `
  }
}

export const organizerNotificationTemplates = {
  en: {
    subject: 'New Registration: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Rotary Club Tunis Doyen</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">New Event Registration</h2>
          <p>A new registration has been received for your event:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>Event Date:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>Location:</strong> {eventLocation}</p>
          </div>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #004A87;">Attendee Information:</h4>
            <p style="margin: 5px 0;"><strong>Name:</strong> {attendeeName}</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> {attendeeEmail}</p>
            <p style="margin: 5px 0;"><strong>Registration Date:</strong> {registrationDate}</p>
          </div>
          <p>You can view all registrations in the admin panel.</p>
        </div>
      </div>
    `,
    text: `
      Rotary Club Tunis Doyen - New Event Registration

      A new registration has been received for your event:

      Event: {eventTitle}
      Date: {eventDate}
      Location: {eventLocation}

      Attendee Information:
      Name: {attendeeName}
      Email: {attendeeEmail}
      Registration Date: {registrationDate}

      You can view all registrations in the admin panel.
    `
  },
  fr: {
    subject: 'Nouvelle Inscription: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Rotary Club Tunis Doyen</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">Nouvelle Inscription</h2>
          <p>Une nouvelle inscription a été reçue pour votre événement:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>Date:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>Lieu:</strong> {eventLocation}</p>
          </div>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #004A87;">Informations sur le participant:</h4>
            <p style="margin: 5px 0;"><strong>Nom:</strong> {attendeeName}</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> {attendeeEmail}</p>
            <p style="margin: 5px 0;"><strong>Date d'inscription:</strong> {registrationDate}</p>
          </div>
          <p>Vous pouvez voir toutes les inscriptions dans le panneau d'administration.</p>
        </div>
      </div>
    `,
    text: `
      Rotary Club Tunis Doyen - Nouvelle Inscription

      Une nouvelle inscription a été reçue pour votre événement:

      Événement: {eventTitle}
      Date: {eventDate}
      Lieu: {eventLocation}

      Informations sur le participant:
      Nom: {attendeeName}
      Email: {attendeeEmail}
      Date d'inscription: {registrationDate}

      Vous pouvez voir toutes les inscriptions dans le panneau d'administration.
    `
  },
  ar: {
    subject: 'تسجيل جديد: {eventTitle}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
        <div style="background: linear-gradient(135deg, #004A87 0%, #FFB400 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">روتاري تونس الدويان</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">People of Action</p>
        </div>
        <div style="padding: 40px 30px; background: #f8f9fa;">
          <h2 style="color: #004A87; margin-bottom: 20px;">تسجيل جديد</h2>
          <p>تم استلام تسجيل جديد لفعاليتك:</p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #004A87;">
            <h3 style="margin: 0 0 10px 0; color: #004A87;">{eventTitle}</h3>
            <p style="margin: 5px 0;"><strong>تاريخ الفعالية:</strong> {eventDate}</p>
            <p style="margin: 5px 0;"><strong>المكان:</strong> {eventLocation}</p>
          </div>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #004A87;">معلومات المشارك:</h4>
            <p style="margin: 5px 0;"><strong>الاسم:</strong> {attendeeName}</p>
            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {attendeeEmail}</p>
            <p style="margin: 5px 0;"><strong>تاريخ التسجيل:</strong> {registrationDate}</p>
          </div>
          <p>يمكنك عرض جميع التسجيلات في لوحة التحكم.</p>
        </div>
      </div>
    `,
    text: `
      روتاري تونس الدويان - تسجيل جديد

      تم استلام تسجيل جديد لفعاليتك:

      الفعالية: {eventTitle}
      التاريخ: {eventDate}
      المكان: {eventLocation}

      معلومات المشارك:
      الاسم: {attendeeName}
      البريد الإلكتروني: {attendeeEmail}
      تاريخ التسجيل: {registrationDate}

      يمكنك عرض جميع التسجيلات في لوحة التحكم.
    `
  }
}

// Template interpolation function
import sanitizeHtml from 'sanitize-html'; // Add this import

// ... existing code ...

export const interpolateTemplate = (
  template: { subject: string; html: string; text: string },
  variables: Record<string, string>
): { subject: string; html: string; text: string } => {
  const interpolate = (text: string): string => {
    return text.replace(/{(\\w+)}/g, (match, key) => variables[key] || match);
  };

  const interpolatedHtml = interpolate(template.html);
  // Sanitize HTML content to prevent XSS
  const sanitizedHtml = sanitizeHtml(interpolatedHtml, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat(['img', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'span', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'hr', 'table', 'thead', 'tbody', 'tr', 'th', 'td']),
    allowedAttributes: {
      a: ['href', 'name', 'target'],
      img: ['src', 'alt'],
      div: ['style'], // Allow style attribute for basic inline styling
      p: ['style'],
      span: ['style'],
      h1: ['style'],
      h2: ['style'],
      h3: ['style'],
      h4: ['style'],
      h5: ['style'],
      h6: ['style'],
      strong: ['style'],
      em: ['style'],
      ul: ['style'],
      ol: ['style'],
      li: ['style'],
      table: ['style', 'width', 'cellpadding', 'cellspacing'],
      thead: ['style'],
      tbody: ['style'],
      tr: ['style'],
      th: ['style'],
      td: ['style'],
    },
    // You might want to configure allowed styles more strictly based on your needs
    // For example, only allowing specific CSS properties
    allowedStyles: {
      '*': {
        'font-family': [/^[\\w\\s,-]+$/],
        'max-width': [/^\d+(px|em|%)$/],
        'margin': [/^\d+(px|em|%)(\s\d+(px|em|%)){0,3}$/],
        'padding': [/^\d+(px|em|%)(\s\d+(px|em|%)){0,3}$/],
        'text-align': ['left', 'right', 'center', 'justify'],
        'background': [/^[\w\s\(\)#,.-]+$/],
        'color': [/^[\w\s\(\)#,.-]+$/],
        'font-size': [/^\d+(px|em|%)$/],
        'border-radius': [/^\d+(px|em|%)$/],
        'border-left': [/^(\d+px\s\w+\s[\w#]+)$/],
        'border-right': [/^(\d+px\s\w+\s[\w#]+)$/],
        'direction': ['ltr', 'rtl'],
        'width': [/^\d+(px|em|%)$/],
      }
    }
  });

  return {
    subject: interpolate(template.subject),
    html: sanitizedHtml,
    text: interpolate(template.text)
  };
};