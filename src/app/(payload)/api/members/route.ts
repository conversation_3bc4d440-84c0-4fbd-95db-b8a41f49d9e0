import { NextRequest } from 'next/server'
import { getPayload } from 'payload'
import configPromise from '@payload-config'

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100

// In-memory store for rate limiting (replace with Red<PERSON> in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return true
}

function getClientId(request: NextRequest): string {
  // Use a combination of IP and user agent for identification
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  return `${ip}:${userAgent.slice(0, 100)}` // Limit user agent length
}

// Input sanitization function to prevent XSS
function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  // Remove HTML tags and potentially dangerous characters
  return input.replace(/[<>'"&]/g, '').trim()
}

// Request validation
interface MemberDirectoryRequest {
  search?: string
  district?: string[]
  committee?: string[]
  limit?: number
  page?: number
}


function validateRequest(body: MemberDirectoryRequest): boolean {
  // Validate search string length
  if (body.search && body.search.length > 100) {
    return false
  }

  // Validate pagination parameters
  if (body.limit && (body.limit < 1 || body.limit > 100)) {
    return false
  }

  if (body.page && body.page < 1) {
    return false
  }

  // Validate array parameters
  if (body.district && (!Array.isArray(body.district) || body.district.length > 10)) {
    return false
  }

  if (body.committee && (!Array.isArray(body.committee) || body.committee.length > 20)) {
    return false
  }

  return true
}

// Transform member data for public consumption
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function transformMemberData(user: any) {
  return {
    id: user.id,
    name: user.name,
    classification: user.classification,
    joiningDate: user.joiningDate,
    rotaryId: user.rotaryId,
    rotaryDistrict: user.rotaryDistrict,
    rotaryClub: user.rotaryClub,

    // Only include contact data if explicitly consented
    ...(user.privacySettings?.shareContactDetails && {
      phonePersonal: user.phonePersonal,
      phoneWork: user.phoneWork,
    }),

    // Always include public leadership information
    ...(user.leadershipRoles && {
      leadershipRoles: user.leadershipRoles
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .filter((role: any) => !role.isPrivate)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((role: any) => ({
          position: role.position,
          scope: role.scope,
          startYear: role.startYear,
          isCurrent: role.isCurrent,
        }))
    }),

    // Include public committee memberships
    ...(user.committees && {
      committees: user.committees
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .filter((committee: any) => !committee.isPrivate)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((committee: any) => ({
          committee: committee.committee,
          startDate: committee.startDate,
          isActive: committee.isActive,
        }))
    }),

    // Service projects summary (public only)
    ...(user.serviceProjects && {
      serviceProjectCount: user.serviceProjects.length,
      publicServiceProjects: user.serviceProjects
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .filter((project: any) => project.isPublic !== false)
        .slice(0, 3) // Limit to 3 projects for performance
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((project: any) => ({
          projectName: project.projectName,
          projectType: project.projectType,
          year: project.year,
        }))
    }),

    // Privacy indicator
    showContact: user.privacySettings?.shareContactDetails || false,
    showPhotos: user.privacySettings?.sharePhotos || false,
  }
}

export async function GET(request: NextRequest) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: configPromise })

    // Rate limiting check
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests, please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const requestData: MemberDirectoryRequest = {
      search: searchParams.get('search') || undefined,
      district: searchParams.get('district')?.split(',') || undefined,
      committee: searchParams.get('committee')?.split(',') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined,
    }

    // Validate request parameters
    if (!validateRequest(requestData)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid request parameters.',
          code: 'INVALID_PARAMETERS'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    const {
      search,
      district,
      committee,
      limit = 50,
      page = 1
    } = requestData

    // Sanitize search input
    const sanitizedSearch = search ? sanitizeInput(search) : undefined
    const sanitizedDistrict = district?.map(d => sanitizeInput(d))
    const sanitizedCommittee = committee?.map(c => sanitizeInput(c))

    // Build MongoDB query for privacy-aware member filtering
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const query: any = {
      'privacySettings.isPublicProfile': { $eq: true }
    }

    // Add search conditions if provided
    if (sanitizedSearch) {
      query.$or = [
        { name: { $regex: sanitizedSearch, $options: 'i' } },
        { classification: { $regex: sanitizedSearch, $options: 'i' } },
        { 'leadershipRoles.position': { $regex: sanitizedSearch, $options: 'i' } },
      ]
    }

    // Add district filter if provided
    if (sanitizedDistrict && sanitizedDistrict.length > 0) {
      query.rotaryDistrict = { $in: sanitizedDistrict }
    }

    // Add committee filter if provided
    if (sanitizedCommittee && sanitizedCommittee.length > 0) {
      query['committees.committee'] = { $in: sanitizedCommittee }
    }


    console.log('Payload is ready. Attempting to find members.');
    console.log('DATABASE_URI:', process.env.DATABASE_URI); // Temporary log for debugging

    // Execute query with optimal pagination
    const members = await payload.find({
      collection: 'users',
      where: query,
      limit,
      page,
      depth: 2, // Include nested field data without excessive depth
      sort: 'name', // Alphabetical ordering by name
    })

    // Transform data for privacy compliance
    const transformedMembers = members.docs.map(transformMemberData)

    // Return privacy-aware response
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          members: transformedMembers,
          total: members.totalDocs,
          limit: members.limit,
          page: members.page,
          totalPages: members.totalPages,
          hasNextPage: members.hasNextPage,
          hasPrevPage: members.hasPrevPage,
        },
        meta: {
          searched: sanitizedSearch || null,
          districtFilters: sanitizedDistrict || [],
          committeeFilters: sanitizedCommittee || [],
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300', // 5 minute cache
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Member directory error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while fetching member directory.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Health check endpoint
export async function HEAD() {
  return new Response(null, {
    status: 200,
    headers: {
      'X-Service-Status': 'healthy',
      'X-API-Version': 'v1',
    }
  })
}