// Enterprise-grade event registration API - Task 1.3.1 Implementation
// Following Task 1.2.4 security hardening and error handling patterns

import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import type { User } from '@/payload-types'

// Security configuration following Task 1.2.4 patterns
const REGISTRATION_CONFIG = {
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15 minutes
    MAX_REQUESTS: 5,
  },
  TIMEOUT_MS: 30000,
} as const

// Rate limiting for registration prevention
const registrationRateLimit = new Map<string, { count: number; resetTime: number }>()
const CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // Clean up every hour

function cleanupRateLimitMap() {
  const now = Date.now();
  for (const [clientId, client] of registrationRateLimit.entries()) {
    if (now > client.resetTime) {
      registrationRateLimit.delete(clientId);
    }
  }
  console.log(`[CLEANUP] Rate limit map cleaned. Current size: ${registrationRateLimit.size}`);
}

// Start cleanup interval on server startup (or when this module is first loaded)
setInterval(cleanupRateLimitMap, CLEANUP_INTERVAL_MS);

function checkRegistrationRateLimit(clientId: string): { allowed: boolean; retryAfter?: number } {
  const now = Date.now()
  const client = registrationRateLimit.get(clientId)

  if (!client || now > client.resetTime) {
    registrationRateLimit.set(clientId, {
      count: 1,
      resetTime: now + REGISTRATION_CONFIG.RATE_LIMIT.WINDOW_MS
    })
    return { allowed: true }
  }

  if (client.count >= REGISTRATION_CONFIG.RATE_LIMIT.MAX_REQUESTS) {
    return { allowed: false, retryAfter: Math.ceil((client.resetTime - now) / 1000) } // seconds
  }

  client.count++
  return { allowed: true }
}

// Enhanced authentication following Task 1.2.4 patterns
async function authenticateUser(request: NextRequest): Promise<{
  user: User | null;
  errorCode?: string
}> {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return { user: null, errorCode: 'MISSING_TOKEN' }
    }

    const payload = await getPayload({ config: configPromise })

    // Add timeout to authentication
    let authTimedOut = false;
    const authPromise = payload.auth({
      headers: new Headers({
        'content-type': 'application/json',
      })
    });

    const timeoutId = setTimeout(() => {
      authTimedOut = true;
      // Optionally, you could try to abort the fetch if payload.auth used fetch directly and exposed an AbortController
    }, REGISTRATION_CONFIG.TIMEOUT_MS);

    try {
      const authResult = await authPromise;
      clearTimeout(timeoutId); // Clear timeout if auth resolves first
      if (authTimedOut) { // If auth resolves after timeout, ignore its result
        return { user: null, errorCode: 'AUTH_TIMEOUT' };
      }
      return { user: authResult.user as User };
    } catch (error) {
      clearTimeout(timeoutId); // Clear timeout on error
      if (authTimedOut) { // If error is due to timeout, handle it
        return { user: null, errorCode: 'AUTH_TIMEOUT' };
      }
      throw error; // Re-throw other errors
    }

  } catch (error) {
    console.error('Registration authentication error:', error)
    return { user: null, errorCode: 'AUTH_FAILED' }
  }
}

// Enterprise validation following Task 1.2.4 patterns
const registrationValidators = {
  email: (email: string): boolean =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),

  phone: (phone: string): boolean =>
    /^\+?[\d\s\-\(\)]{10,20}$/.test(phone) && phone.replace(/\D/g, '').length >= 10,

  required: (value: string): boolean =>
    value !== undefined && value !== null && value.trim().length > 0,

  rotaryId: (rotaryId: string): boolean => {
    if (!rotaryId.trim()) return true // Optional field
    return /^\d{1,8}$/.test(rotaryId.replace(/^RI/i, ''))
  },

  capacity: (currentAttendees: number, capacity: number): boolean => {
    if (!capacity) return true
    return currentAttendees < capacity
  },

  duplicateEmail: (email: string, attendees: any[]): boolean => {
    return !attendees.some(attendee => attendee.userEmail === email)
  }
}

// Input sanitization following Task 1.2.4 security patterns
function sanitizeRegistrationInput(input: any): {
  eventId: string
  attendee: {
    name: string
    email: string
    phone: string
    classification: string
    rotaryId?: string
    consentDataProcessing: boolean
    marketingConsent: boolean
    registrationDate: string
  }
} {
  const sanitizeText = (text: string): string => {
    if (typeof text !== 'string') return ''
    return text.replace(/[<>'"&]/g, '').trim()
  }

  const sanitizeEmail = (email: string): string => {
    if (typeof email !== 'string') return ''
    return email.toLowerCase().trim()
  }

  const attendee = input.attendee

  const sanitizedAttendee = Object.create(null); // Create a null-prototype object
  sanitizedAttendee.name = sanitizeText(attendee.name);
  sanitizedAttendee.email = sanitizeEmail(attendee.email);
  sanitizedAttendee.phone = sanitizeText(attendee.phone);
  sanitizedAttendee.classification = sanitizeText(attendee.classification);
  sanitizedAttendee.rotaryId = attendee.rotaryId ? sanitizeText(attendee.rotaryId) : undefined;
  sanitizedAttendee.consentDataProcessing = Boolean(attendee.consentDataProcessing);
  sanitizedAttendee.marketingConsent = Boolean(attendee.marketingConsent);
  sanitizedAttendee.registrationDate = new Date().toISOString();

  return {
    eventId: sanitizeText(input.eventId),
    attendee: sanitizedAttendee
  }
}

// Validation errors
function createValidationError(message: string, field?: string): {
  code: string
  message: string
  field?: string
} {
  return {
    code: 'VALIDATION_ERROR',
    message,
    field
  }
}

// POST /api/events/register - Register attendee for event
export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    // Step 1: Authentication
    const auth = await authenticateUser(request)
    if (!auth.user?.id) {
      return NextResponse.json({
        error: 'Authentication required',
        code: auth.errorCode,
      }, { status: 401 })
    }

    // Step 2: Rate limiting
    const clientId = `${auth.user.id}_${request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown'}`
    const rateLimitStatus = checkRegistrationRateLimit(clientId)
    if (!rateLimitStatus.allowed) {
      return NextResponse.json({
        error: 'Too many registration attempts, please try again later',
        code: 'RATE_LIMIT_EXCEEDED',
      }, {
        status: 429,
        headers: { 'Retry-After': rateLimitStatus.retryAfter?.toString() || '60' } // Default to 60 seconds
      })
    }

    // Step 3: Parse and validate input
    let rawData;
    try {
      rawData = await request.json()
    } catch (error) {
      return NextResponse.json(createValidationError('Invalid JSON in request body'), { status: 400 })
    }
    
    if (!rawData?.eventId || !rawData?.attendee) {
      return NextResponse.json(createValidationError('Invalid registration data'), { status: 400 })
    }

    // Validate event ID format
    if (typeof rawData.eventId !== 'string' || rawData.eventId.trim().length === 0) {
      return NextResponse.json(createValidationError('Invalid event ID'), { status: 400 })
    }

    const data = sanitizeRegistrationInput(rawData)

    // Step 4: Validate attendee data
    const validationErrors: any[] = []

    if (!registrationValidators.required(data.attendee.name)) {
      validationErrors.push(createValidationError('Name is required', 'name'))
    } else if (data.attendee.name.length > 100) {
      validationErrors.push(createValidationError('Name must be less than 100 characters', 'name'))
    }

    if (!registrationValidators.required(data.attendee.email)) {
      validationErrors.push(createValidationError('Email is required', 'email'))
    } else if (!registrationValidators.email(data.attendee.email)) {
      validationErrors.push(createValidationError('Invalid email format', 'email'))
    } else if (data.attendee.email.length > 254) {
      validationErrors.push(createValidationError('Email must be less than 254 characters', 'email'))
    }

    if (!registrationValidators.required(data.attendee.phone)) {
      validationErrors.push(createValidationError('Phone is required', 'phone'))
    } else if (!registrationValidators.phone(data.attendee.phone)) {
      validationErrors.push(createValidationError('Invalid phone format', 'phone'))
    } else if (data.attendee.phone.length > 20) {
      validationErrors.push(createValidationError('Phone must be less than 20 characters', 'phone'))
    }

    if (!registrationValidators.required(data.attendee.classification)) {
      validationErrors.push(createValidationError('Classification is required', 'classification'))
    } else if (data.attendee.classification.length > 100) {
      validationErrors.push(createValidationError('Classification must be less than 100 characters', 'classification'))
    }

    if (data.attendee.rotaryId) {
      if (!registrationValidators.rotaryId(data.attendee.rotaryId)) {
        validationErrors.push(createValidationError('Invalid Rotary ID format', 'rotaryId'))
      } else if (data.attendee.rotaryId.length > 20) {
        validationErrors.push(createValidationError('Rotary ID must be less than 20 characters', 'rotaryId'))
      }
    }

    if (!data.attendee.consentDataProcessing) {
      validationErrors.push(createValidationError('Data processing consent is required'))
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return NextResponse.json({
        error: 'Validation failed',
        code: 'VALIDATION_FAILED',
        details: validationErrors
      }, { status: 400 })
    }

    // Step 5: Initialize Payload and fetch event
    const payload = await getPayload({ config: configPromise })

    // Fetch event with current attendees
    let eventResult;
    try {
      eventResult = await payload.findByID({
        collection: 'events',
        id: data.eventId,
        depth: 2,
      })
    } catch (error) {
      console.error('Error fetching event:', error)
      return NextResponse.json({
        error: 'Failed to fetch event data',
        code: 'EVENT_FETCH_ERROR',
      }, { status: 500 })
    }

    if (!eventResult) {
      return NextResponse.json({
        error: 'Event not found',
        code: 'EVENT_NOT_FOUND',
      }, { status: 404 })
    }

    const event = eventResult
    const currentAttendees = event.attendees || []

    // Step 6: Business rule validation
    if (!event.registrationRequired) {
      return NextResponse.json({
        error: 'Registration is not required for this event',
        code: 'REGISTRATION_NOT_ALLOWED',
      }, { status: 400 })
    }

    if (event.status !== 'published') {
      return NextResponse.json({
        error: 'Event is not available for registration',
        code: 'EVENT_NOT_AVAILABLE',
      }, { status: 400 })
    }

    // Additional business rule: Check if event date has passed
    if (event.eventDate) {
      const eventDate = new Date(event.eventDate);
      const now = new Date();
      if (eventDate < now) {
        return NextResponse.json({
          error: 'Cannot register for past events',
          code: 'EVENT_IN_PAST',
        }, { status: 400 })
      }
    }

    // Step 7: Duplicate check (capacity check moved to update phase)
    if (!registrationValidators.duplicateEmail(data.attendee.email, currentAttendees)) {
      return NextResponse.json({
        error: 'You are already registered for this event',
        code: 'DUPLICATE_REGISTRATION',
      }, { status: 400 })
    }

    // Step 8: Registration date check (if specified)
    if (event.registrationForm?.registrationDeadline) {
      const deadline = new Date(event.registrationForm.registrationDeadline)
      if (new Date() > deadline) {
        return NextResponse.json({
          error: 'Registration deadline has passed',
          code: 'REGISTRATION_CLOSED',
        }, { status: 400 })
      }
    }

    // Step 9: Create attendee record
    const newAttendee = {
      userId: auth.user.id,
      userName: (auth.user.name as string) || data.attendee.name,
      userEmail: data.attendee.email,
      registrationDate: data.attendee.registrationDate,
      status: 'registered' as const,
      attendeeInfo: {
        name: data.attendee.name,
        phone: data.attendee.phone,
        classification: data.attendee.classification,
        rotaryId: data.attendee.rotaryId,
        consents: {
          dataProcessing: data.attendee.consentDataProcessing,
          marketing: data.attendee.marketingConsent,
        }
      }
    }

    // Step 10: Implement optimistic locking for capacity check and update
    // Re-fetch the event to get the latest state before updating
    const latestEvent = await payload.findByID({
      collection: 'events',
      id: data.eventId,
      depth: 1, // Only need attendees for capacity check
    });

    if (!latestEvent) {
      return NextResponse.json({
        error: 'Event not found during final capacity check',
        code: 'EVENT_NOT_FOUND_CONCURRENT',
      }, { status: 404 });
    }

    const latestAttendees = latestEvent.attendees || [];

    // Enhanced capacity validation with better error messaging
    const capacity = latestEvent.capacity || 0;
    const currentCount = latestAttendees.length;
    
    if (capacity > 0 && currentCount >= capacity) {
      return NextResponse.json({
        error: 'Event is at capacity. Another registration was just processed.',
        code: 'EVENT_FULL_CONCURRENT',
        details: {
          capacity,
          currentCount,
          spotsRemaining: 0
        }
      }, { status: 400 });
    }

    const updatedEvent = await payload.update({
      collection: 'events',
      id: data.eventId,
      data: {
        attendees: [...latestAttendees, newAttendee], // Use latest attendees
        updatedAt: new Date().toISOString(),
      }
    })

    // Step 11: Calculate capacity status
    const newAttendees = updatedEvent.attendees || []
    const remainingCapacity = event.capacity ? event.capacity - newAttendees.length : null

    // Step 12: Performance logging
    const duration = Date.now() - startTime
    console.log(`[PERF] Event registration completed in ${duration}ms`, {
      eventId: data.eventId,
      userId: auth.user.id,
      email: data.attendee.email,
      remainingCapacity,
    })

    // Step 13: Clear rate limiting on success
    registrationRateLimit.delete(clientId)

    // Step 14: Return success response
    const responseTime = Date.now() - startTime
    return NextResponse.json({
      success: true,
      message: 'Registration successful',
      data: {
        eventId: data.eventId,
        attendee: newAttendee,
        remainingCapacity,
        registrationId: `reg_${data.eventId}_${Date.now()}`,
        metadata: {
          responseTimeMs: responseTime,
          registrationDate: new Date().toISOString(),
          capacity: event.capacity,
          totalAttendees: newAttendees.length,
        }
      },
      meta: {
        rateLimitRemaining: REGISTRATION_CONFIG.RATE_LIMIT.MAX_REQUESTS,
        responseTimeMs: responseTime,
      }
    }, {
      status: 201,
      headers: {
        'X-Response-Time': `${responseTime}ms`,
      }
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`[ERROR] Event registration failed after ${duration}ms:`, error)

    return NextResponse.json({
      error: 'Registration failed due to server error',
      code: 'INTERNAL_ERROR',
      metadata: {
        responseTimeMs: duration,
        timestamp: new Date().toISOString(),
      }
    }, { status: 500 })
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
    }
  })
}