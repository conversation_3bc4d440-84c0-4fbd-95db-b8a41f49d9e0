import { exportEventAttendees } from '@/utilities/eventExports'
import { getPayload } from 'payload'
import { NextRequest } from 'next/server'
import configPromise from '@payload-config'

// GET /api/events/[id]/export - Export event attendees as CSV
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: eventId } = await params
    const payload = await getPayload({ config: configPromise })

    // Fetch the event with attendee data
    const event = await payload.findByID({
      collection: 'events',
      id: eventId,
      depth: 1,
    })

    if (!event) {
      return new Response('Event not found', { status: 404 })
    }

    // Generate CSV export data
    const { filename, content, mimeType } = exportEventAttendees(event)

    // Return CSV file response
    return new Response(content, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })

  } catch (error) {
    console.error('Export error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

// POST /api/events/[id]/export - Alternative POST method for compatibility
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return GET(req, { params })
}