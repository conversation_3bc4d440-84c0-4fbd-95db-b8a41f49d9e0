import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { getAuthenticatedUser } from '../../../../../utilities/getAuthenticatedUser'

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100

// In-memory store for rate limiting (replace with Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return true
}

function getClientId(request: Request): string {
  // Use a combination of IP and user agent for identification
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  return `${ip}:${userAgent.slice(0, 100)}` // Limit user agent length
}

// Request validation
interface PrivacySettingsUpdateRequest {
  privacySettings?: {
    isPublicProfile?: boolean
    shareContactDetails?: boolean
    sharePhotos?: boolean
    marketingConsent?: boolean
    dataSharingConsent?: boolean
  }
}

function validatePrivacyUpdate(body: PrivacySettingsUpdateRequest): boolean {
  // Validate privacy settings object
  if (body.privacySettings && typeof body.privacySettings !== 'object') return false

  // Validate individual privacy settings
  const { privacySettings } = body
  if (privacySettings) {
    if (privacySettings.isPublicProfile !== undefined && typeof privacySettings.isPublicProfile !== 'boolean') return false
    if (privacySettings.shareContactDetails !== undefined && typeof privacySettings.shareContactDetails !== 'boolean') return false
    if (privacySettings.sharePhotos !== undefined && typeof privacySettings.sharePhotos !== 'boolean') return false
    if (privacySettings.marketingConsent !== undefined && typeof privacySettings.marketingConsent !== 'boolean') return false
    if (privacySettings.dataSharingConsent !== undefined && typeof privacySettings.dataSharingConsent !== 'boolean') return false
  }

  return true
}

export async function PUT(request: Request) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: configPromise })

    // Rate limiting check
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests, please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Get authenticated user
    const user = await getAuthenticatedUser(request, payload)

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const body: PrivacySettingsUpdateRequest = await request.json()

    // Validate request
    if (!validatePrivacyUpdate(body)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid request data.',
          code: 'INVALID_DATA'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Update privacy settings
    const updatedUser = await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        privacySettings: body.privacySettings
      },
      depth: 2
    })

    // Remove sensitive fields
    const { password: _, salt: __, ...safeProfile } = updatedUser

    // Return updated profile
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Privacy settings updated successfully',
        data: {
          user: safeProfile
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Privacy settings update error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while updating privacy settings.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}