import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { getAuthenticatedUser } from '../../../../../utilities/getAuthenticatedUser'

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100

// In-memory store for rate limiting (replace with Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return true
}

function getClientId(request: Request): string {
  // Use a combination of IP and user agent for identification
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  return `${ip}:${userAgent.slice(0, 100)}` // Limit user agent length
}

// Request validation
interface CommunicationPreferencesUpdateRequest {
  communicationPreferences?: {
    emailNotifications?: boolean
    newsletterSubscription?: boolean
    meetingReminders?: boolean
    committeeUpdates?: boolean
  }
}

function validateCommunicationUpdate(body: CommunicationPreferencesUpdateRequest): boolean {
  // Validate communication preferences object
  if (body.communicationPreferences && typeof body.communicationPreferences !== 'object') return false

  // Validate individual communication preferences
  const { communicationPreferences } = body
  if (communicationPreferences) {
    if (communicationPreferences.emailNotifications !== undefined && typeof communicationPreferences.emailNotifications !== 'boolean') return false
    if (communicationPreferences.newsletterSubscription !== undefined && typeof communicationPreferences.newsletterSubscription !== 'boolean') return false
    if (communicationPreferences.meetingReminders !== undefined && typeof communicationPreferences.meetingReminders !== 'boolean') return false
    if (communicationPreferences.committeeUpdates !== undefined && typeof communicationPreferences.committeeUpdates !== 'boolean') return false
  }

  return true
}

export async function PUT(request: Request) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: configPromise })

    // Rate limiting check
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests, please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Get authenticated user
    const user = await getAuthenticatedUser(request, payload)

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const body: CommunicationPreferencesUpdateRequest = await request.json()

    // Validate request
    if (!validateCommunicationUpdate(body)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid request data.',
          code: 'INVALID_DATA'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Update communication preferences
    const updatedUser = await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        communicationPreferences: body.communicationPreferences
      },
      depth: 2
    })

    // Remove sensitive fields
    const { password: _, salt: __, ...safeProfile } = updatedUser

    // Return updated profile
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Communication preferences updated successfully',
        data: {
          user: safeProfile
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Communication preferences update error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while updating communication preferences.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}