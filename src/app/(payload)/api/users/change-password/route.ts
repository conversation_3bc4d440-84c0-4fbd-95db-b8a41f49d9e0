import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { getAuthenticatedUser } from '../../../../../utilities/getAuthenticatedUser'

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 5 // Lower limit for password changes

// In-memory store for rate limiting (replace with Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return true
}

function getClientId(request: Request): string {
  // Use a combination of IP and user agent for identification
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  return `${ip}:${userAgent.slice(0, 100)}` // Limit user agent length
}

// Request validation
interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
}

function validatePasswordChange(body: PasswordChangeRequest): boolean {
  // Validate that both passwords are provided
  if (!body.currentPassword || !body.newPassword) return false

  // Validate password length
  if (body.currentPassword.length < 8 || body.newPassword.length < 8) return false

  // Validate password complexity (basic check)
  if (body.newPassword.length > 128) return false

  return true
}

export async function POST(request: Request) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: configPromise })

    // Rate limiting check
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return new Response(
        JSON.stringify({
          error: 'Too many password change attempts. Please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Get authenticated user
    const user = await getAuthenticatedUser(request, payload)

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const clonedRequest = request.clone();
    const body: PasswordChangeRequest = await clonedRequest.json()

    // Validate request
    if (!validatePasswordChange(body)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid password data. Passwords must be at least 8 characters long.',
          code: 'INVALID_DATA'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Verify current password
    try {
      const loginResult = await payload.login({
        collection: 'users',
        data: {
          email: user.email,
          password: body.currentPassword
        }
      })

      if (!loginResult.user) {
        return new Response(
          JSON.stringify({
            error: 'Current password is incorrect.',
            code: 'INVALID_PASSWORD'
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
    } catch (loginError) {
      console.error('Password verification error:', loginError)
      return new Response(
        JSON.stringify({
          error: 'Current password is incorrect.',
          code: 'INVALID_PASSWORD'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Update password
    await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        password: body.newPassword
      }
    })

    // Return success message
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Password changed successfully'
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Password change error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while changing password.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}