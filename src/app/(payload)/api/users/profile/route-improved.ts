import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import type { User } from '@/payload-types'

// Centralized configuration for maintainability
const API_CONFIG = {
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15 minutes
    MAX_REQUESTS: 10,
  },
  DATABASE: {
    TIMEOUT_MS: 10000, // 10 second timeout for database operations
  },
  SECURITY: {
    LOG_SANITATION_LEVEL: 'INFO' as 'ERROR' | 'WARN' | 'INFO' | 'DEBUG', // ERROR, WARN, INFO, DEBUG
  },
} as const

// Enhanced rate limiting with sliding window and memory optimization
class RateLimiter {
  private store = new Map<string, { count: number; resetTime: number }>()

  check(userId: string): boolean {
    const now = Date.now()
    const client = this.store.get(userId)

    if (!client || now > client.resetTime) {
      this.store.set(userId, {
        count: 1,
        resetTime: now + API_CONFIG.RATE_LIMIT.WINDOW_MS
      })
      return true
    }

    if (client.count >= API_CONFIG.RATE_LIMIT.MAX_REQUESTS) {
      return false
    }

    client.count++
    return true
  }

  getRemainingRequests(userId: string): number {
    const client = this.store.get(userId)
    if (!client) return API_CONFIG.RATE_LIMIT.MAX_REQUESTS

    return Math.max(0, API_CONFIG.RATE_LIMIT.MAX_REQUESTS - client.count)
  }

  getResetTime(userId: string): number | null {
    return this.store.get(userId)?.resetTime || null
  }

  // Memory cleanup for expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [userId, client] of this.store) {
      if (now > client.resetTime) {
        this.store.delete(userId)
      }
    }
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter()

// Background cleanup interval
if (typeof globalThis !== 'undefined') {
  setInterval(() => rateLimiter.cleanup(), 300000) // Clean every 5 minutes
}

// Enhanced sanitization with configurable patterns
function sanitizeInput(input: string, options: { allowHtml?: boolean } = {}): string {
  if (typeof input !== 'string') return ''

  let sanitized = input.trim()

  if (!options.allowHtml) {
    // Remove potential XSS vectors
    sanitized = sanitized
      .replace(/[<>'"&]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
  }

  return sanitized
}

// Zod-inspired validation schema
const validators = {
  phoneNumber: (phone: string): boolean =>
    /^\+?[\d\s\-\(\)]+$/.test(phone) && phone.length >= 8 && phone.length <= 20,

  rotaryId: (rotaryId: string): boolean =>
    /^[A-Za-z0-9\-_\s]{3,20}$/.test(rotaryId),

  emailFormat: (email: string): boolean =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),

  localizedText: (text: Record<string, string>): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    const locales = ['en', 'fr', 'ar'] as const

    if (typeof text !== 'object' || text === null) {
      errors.push('Localized text must be an object')
      return { valid: false, errors }
    }

    const hasAtLeastOne = locales.some(locale =>
      text[locale] && typeof text[locale] === 'string' && text[locale].trim().length > 0
    )

    if (!hasAtLeastOne) {
      errors.push('At least one language must be provided')
    }

    return { valid: errors.length === 0, errors }
  },

  privacySettings: (settings: Record<string, unknown>): { valid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (typeof settings !== 'object') {
      errors.push('Privacy settings must be an object')
      return { valid: false, errors }
    }

    const requiredKeys = ['isPublicProfile', 'shareContactDetails', 'sharePhotos', 'marketingConsent', 'dataSharingConsent']

    requiredKeys.forEach(key => {
      if (!(key in settings)) {
        errors.push(`Missing privacy setting: ${key}`)
      } else if (typeof settings[key] !== 'boolean') {
        errors.push(`Privacy setting ${key} must be boolean`)
      }
    })

    return { valid: errors.length === 0, errors }
  }
}

// Enhanced authentication with timeout and retry logic
async function authenticateUser(_request: NextRequest): Promise<{ user: User | null; errorCode?: string }> {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return { user: null, errorCode: 'MISSING_TOKEN' }
    }

    const payload = await getPayload({ config: configPromise })

    // Add timeout to authentication
    const authPromise = payload.auth({
      headers: new Headers({
        'content-type': 'application/json',
        // For Payload CMS, authentication is typically handled via cookie
        // Remove custom authorization header as it may conflict with Payload's auth
      })
    })

    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Auth timeout')), API_CONFIG.DATABASE.TIMEOUT_MS)
    )

    const authResult = await Promise.race([authPromise, timeoutPromise])

    return { user: authResult.user as User }

  } catch (error) {
    console.error('Authentication error:', error)
    return { user: null, errorCode: 'AUTH_FAILED' }
  }
}

// Utility function for sanitized error logging
function logError(context: string, error: Error | unknown, request: NextRequest, userId?: string): void {
  const sanitizedError = {
    message: error instanceof Error ? sanitizeInput(error.message) : 'Unknown error',
    stack: API_CONFIG.SECURITY.LOG_SANITATION_LEVEL === 'INFO' ?
      error instanceof Error ? error.stack : undefined : undefined,
    userId: userId || 'anonymous',
    ip: sanitizeInput(request.headers.get('x-forwarded-for') || 'unknown'),
    userAgent: sanitizeInput(request.headers.get('user-agent')?.slice(0, 200) || 'unknown'),
    timestamp: new Date().toISOString(),
    url: request.url,
  }

  console.error(`[${context}]`, sanitizedError)
}

// Performance monitoring utility
function createPerformanceTimer(operation: string) {
  const startTime = Date.now()
  return {
    end: (success: boolean = true, metadata: Record<string, string | number | boolean | undefined> = {}) => {
      const duration = Date.now() - startTime
      console.log(`[PERF] ${operation}: ${duration}ms`, {
        success,
        ...metadata,
        timestamp: new Date().toISOString()
      })
      return duration
    }
  }
}

// Enhanced profile update validation
function validateProfileUpdate(data: any): {
  valid: boolean
  errors: string[]
  sanitizedData: Record<string, any>
} {
  const errors: string[] = []
  const sanitizedData: Record<string, any> = {}

  // Validate name (localized)
  if (data.name) {
    const nameValidation = validators.localizedText(data.name)
    if (!nameValidation.valid) {
      errors.push(...nameValidation.errors.map(err => `Name: ${err}`))
    } else {
      sanitizedData.name = {}
      const locales = ['en', 'fr', 'ar']
      locales.forEach(locale => {
        if (data.name[locale]) {
          sanitizedData.name[locale] = sanitizeInput(data.name[locale])
        }
      })
    }
  }

  // Validate classification (localized)
  if (data.classification) {
    const classValidation = validators.localizedText(data.classification)
    if (!classValidation.valid) {
      errors.push(...classValidation.errors.map(err => `Classification: ${err}`))
    } else {
      sanitizedData.classification = {}
      const locales = ['en', 'fr', 'ar']
      locales.forEach(locale => {
        if (data.classification[locale]) {
          sanitizedData.classification[locale] = sanitizeInput(data.classification[locale])
        }
      })
    }
  }

  // Validate phone numbers
  if (data.phonePersonal && !validators.phoneNumber(data.phonePersonal)) {
    errors.push('Invalid personal phone format')
  } else if (data.phonePersonal) {
    sanitizedData.phonePersonal = sanitizeInput(data.phonePersonal)
  }

  if (data.phoneWork && !validators.phoneNumber(data.phoneWork)) {
    errors.push('Invalid work phone format')
  } else if (data.phoneWork) {
    sanitizedData.phoneWork = sanitizeInput(data.phoneWork)
  }

  // Validate privacy settings
  if (data.privacySettings) {
    const privacyValidation = validators.privacySettings(data.privacySettings)
    if (!privacyValidation.valid) {
      errors.push(...privacyValidation.errors)
    } else {
      sanitizedData.privacySettings = data.privacySettings
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    sanitizedData
  }
}

// Enhanced GET handler with performance monitoring
export async function GET(request: NextRequest) {
  const timer = createPerformanceTimer('GET /api/users/profile')

  try {
    const auth = await authenticateUser(request)
    if (!auth.user?.id) {
      timer.end(false, {
        userId: 'unknown',
        ...(auth.errorCode && { errorCode: auth.errorCode })
      })
      return NextResponse.json(
        { error: 'Authentication required', code: auth.errorCode },
        { status: 401 }
      )
    }

    // Rate limiting check
    if (!rateLimiter.check(auth.user.id)) {
      timer.end(false, { userId: auth.user.id, errorCode: 'RATE_LIMIT_EXCEEDED' })
      return NextResponse.json(
        { error: 'Too many requests, please try again later', code: 'RATE_LIMIT_EXCEEDED' },
        { status: 429 }
      )
    }

    const payload = await getPayload({ config: configPromise })

    // Database operation with timeout
    const dbPromise = payload.findByID({
      collection: 'users',
      id: auth.user.id,
      depth: 3,
    })

    const dbTimer = createPerformanceTimer('database.findByID')
    const user = await dbPromise
    dbTimer.end(true, { userId: auth.user.id })

    if (!user) {
      timer.end(false, { userId: auth.user.id, errorCode: 'USER_NOT_FOUND' })
      return NextResponse.json(
        { error: 'User profile not found', code: 'USER_NOT_FOUND' },
        { status: 404 }
      )
    }

    // Update last login with error handling
    try {
      await payload.update({
        collection: 'users',
        id: auth.user.id,
        data: { lastLogin: new Date().toISOString() },
      })
    } catch (updateError) {
      logError('LAST_LOGIN_UPDATE_FAILED', updateError, request, auth.user.id)
      // Don't fail the request for last login update errors
    }

    const responseTime = timer.end(true, {
      userId: auth.user.id,
      responseTimeMs: Date.now()
    })

    return NextResponse.json({
      success: true,
      data: { user },
      meta: {
        rateLimitRemaining: rateLimiter.getRemainingRequests(auth.user.id),
        rateLimitReset: rateLimiter.getResetTime(auth.user.id),
        responseTimeMs: responseTime,
        timestamp: new Date().toISOString(),
      },
    }, {
      status: 200,
      headers: {
        'X-Response-Time': `${responseTime}ms`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    })

  } catch (error) {
    logError('GET_PROFILE_ERROR', error, request)
    timer.end(false)

    return NextResponse.json({
      error: 'Internal server error occurred.',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
    }, { status: 500 })
  }
}

// Enhanced PUT handler with comprehensive validation
export async function PUT(request: NextRequest) {
  const timer = createPerformanceTimer('PUT /api/users/profile')

  try {
    const auth = await authenticateUser(request)
    if (!auth.user?.id) {
      timer.end(false, { userId: 'unknown', errorCode: auth.errorCode })
      return NextResponse.json(
        { error: 'Authentication required', code: auth.errorCode },
        { status: 401 }
      )
    }

    if (!rateLimiter.check(auth.user.id)) {
      timer.end(false, { userId: auth.user.id, errorCode: 'RATE_LIMIT_EXCEEDED' })
      return NextResponse.json(
        { error: 'Too many requests, please try again later', code: 'RATE_LIMIT_EXCEEDED' },
        { status: 429 }
      )
    }

    const updateData = await request.json()
    const validation = validateProfileUpdate(updateData)

    if (!validation.valid) {
      timer.end(false, { userId: auth.user.id, errorCode: 'VALIDATION_FAILED' })
      return NextResponse.json({
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: validation.errors,
      }, { status: 400 })
    }

    const payload = await getPayload({ config: configPromise })

    // Database update with error handling
    const dbTimer = createPerformanceTimer('database.update')
    const updatedUser = await payload.update({
      collection: 'users',
      id: auth.user.id,
      data: validation.sanitizedData,
    })
    dbTimer.end(true, { userId: auth.user.id })

    // Audit logging with sanitization
    console.log(`[AUDIT] Profile updated:`, {
      userId: auth.user.id,
      fields: Object.keys(validation.sanitizedData),
      timestamp: new Date().toISOString(),
      ip: sanitizeInput(request.headers.get('x-forwarded-for') || 'unknown'),
    })

    const responseTime = timer.end(true, {
      userId: auth.user.id,
      fieldsUpdated: Object.keys(validation.sanitizedData).length
    })

    return NextResponse.json({
      success: true,
      data: { user: updatedUser },
      meta: {
        rateLimitRemaining: rateLimiter.getRemainingRequests(auth.user.id),
        rateLimitReset: rateLimiter.getResetTime(auth.user.id),
        responseTimeMs: responseTime,
        fieldsUpdated: Object.keys(validation.sanitizedData).length,
      },
    }, {
      status: 200,
      headers: {
        'X-Response-Time': `${responseTime}ms`,
      },
    })

  } catch (error) {
    logError('PUT_PROFILE_ERROR', error, request)
    timer.end(false)

    return NextResponse.json({
      error: 'Internal server error occurred during profile update.',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
    }, { status: 500 })
  }
}

// Enhanced PATCH handler with smarter field merging
export async function PATCH(request: NextRequest) {
  // For now, delegate to PUT - could be enhanced with merge logic in future
  return PUT(request)
}

export { rateLimiter }