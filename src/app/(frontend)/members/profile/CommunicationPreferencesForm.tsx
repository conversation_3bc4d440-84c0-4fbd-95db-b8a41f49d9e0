'use client'

import React, { useState } from 'react'

interface User {
  id: string
  name: {
    en?: string
    fr?: string
    ar?: string
  }
  email: string
  phonePersonal?: string
  phoneWork?: string
  classification: {
    en?: string
    fr?: string
    ar?: string
  }
  privacySettings: {
    isPublicProfile: boolean
    shareContactDetails: boolean
    sharePhotos: boolean
    marketingConsent: boolean
    dataSharingConsent?: boolean
  }
  communicationPreferences: {
    emailNotifications: boolean
    newsletterSubscription: boolean
    meetingReminders: boolean
    committeeUpdates: boolean
  }
}

interface CommunicationPreferencesFormProps {
  user: User
  setUser: (user: User) => void
}

export function CommunicationPreferencesForm({ user, setUser }: CommunicationPreferencesFormProps) {
  const [formData, setFormData] = useState({
    emailNotifications: user.communicationPreferences?.emailNotifications || false,
    newsletterSubscription: user.communicationPreferences?.newsletterSubscription || false,
    meetingReminders: user.communicationPreferences?.meetingReminders || false,
    committeeUpdates: user.communicationPreferences?.committeeUpdates || false
  })
  
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleChange = (field: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setSuccess(null)
    setError(null)

    try {
      const response = await fetch('/api/users/communications/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          communicationPreferences: formData
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSuccess('Communication preferences saved successfully!')
        // Update user state with new data
        setUser({
          ...user,
          communicationPreferences: formData
        })
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(data.error || 'Failed to save communication preferences')
      }
    } catch (err) {
      setError('Network error occurred while saving communication preferences')
      console.error('Communication preferences save error:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Communication Preferences</h2>
      
      {success && (
        <div className="mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800 dark:text-green-200">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="emailNotifications"
                name="emailNotifications"
                type="checkbox"
                checked={formData.emailNotifications}
                onChange={(e) => handleChange('emailNotifications', e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="emailNotifications" className="font-medium text-gray-700 dark:text-gray-300">
                Event Updates and Announcements
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Receive email notifications about upcoming events and club announcements.
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="newsletterSubscription"
                name="newsletterSubscription"
                type="checkbox"
                checked={formData.newsletterSubscription}
                onChange={(e) => handleChange('newsletterSubscription', e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="newsletterSubscription" className="font-medium text-gray-700 dark:text-gray-300">
                Monthly Club Newsletter
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Receive the monthly club newsletter with member updates and news.
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="meetingReminders"
                name="meetingReminders"
                type="checkbox"
                checked={formData.meetingReminders}
                onChange={(e) => handleChange('meetingReminders', e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="meetingReminders" className="font-medium text-gray-700 dark:text-gray-300">
                Meeting and Event Reminders
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Receive reminders for scheduled meetings and events.
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="committeeUpdates"
                name="committeeUpdates"
                type="checkbox"
                checked={formData.committeeUpdates}
                onChange={(e) => handleChange('committeeUpdates', e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="committeeUpdates" className="font-medium text-gray-700 dark:text-gray-300">
                Committee Activity Updates
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Receive updates about committee activities when you are a member.
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Communication Preferences'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}