'use client'

import React, { useState, useEffect, useDeferredValue, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'

interface Member {
  id: string
  name: { [key: string]: string }
  classification: { [key: string]: string }
  joiningDate: string
  rotaryId: string
  rotaryDistrict: string
  rotaryClub?: string
  phonePersonal?: string
  phoneWork?: string
  leadershipRoles?: Array<{
    position: { [key: string]: string }
    scope: string
    startYear: number
    isCurrent: boolean
  }>
  committees?: Array<{
    committee: string
    startDate: string
    isActive: boolean
  }>
  serviceProjectCount?: number
  publicServiceProjects?: Array<{
    projectName: string
    projectType: string
    year: number
  }>
  showContact: boolean
  showPhotos: boolean
}

interface FilterState {
  district: string[]
  committee: string[]
}

interface PaginationState {
  page: number
  limit: number
}

interface MemberDirectoryData {
  members: Member[]
  total: number
  limit: number
  page: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface MemberDirectoryClientProps {
  initialData: MemberDirectoryData | null
  error: string | null
  initialSearch: string
  initialFilters: FilterState
  initialPagination: PaginationState
}

// Constants for district and committee options
const DISTRICT_OPTIONS = [
  { label: 'District 1930', value: '1930' },
  { label: 'District 1890', value: '1890' },
  { label: 'Other District', value: 'other' },
]

const COMMITTEE_OPTIONS = [
  { label: 'President Elect Training', value: 'president-elect' },
  { label: 'Director Nominating', value: 'director-nominating' },
  { label: 'Community Service', value: 'community-service' },
  { label: 'Vocational Service', value: 'vocational-service' },
  { label: 'International Service', value: 'international-service' },
  { label: 'Youth Service', value: 'youth-service' },
  { label: 'Membership', value: 'membership' },
  { label: 'Club Service', value: 'club-service' },
]

export function MemberDirectoryClient({
  initialData,
  error,
  initialSearch,
  initialFilters,
  initialPagination
}: MemberDirectoryClientProps) {
  const searchParams = useSearchParams()

  // State management
  const [searchQuery, setSearchQuery] = useState(initialSearch)
  const [filters, setFilters] = useState<FilterState>(initialFilters)
  const [pagination, setPagination] = useState<PaginationState>(initialPagination)
  const [data, setData] = useState<MemberDirectoryData | null>(initialData)
  const [loading, setLoading] = useState(false)
  const [fetchError, setFetchError] = useState<string | null>(error)

  // Debounce search query to prevent excessive API calls
  const deferredSearchQuery = useDeferredValue(searchQuery)

  // Memoized search parameters for URL construction
  const urlParams = useMemo(() => {
    const params = new URLSearchParams()

    if (deferredSearchQuery) {
      params.set('search', deferredSearchQuery)
    }

    if (filters.district.length > 0) {
      params.set('district', filters.district.join(','))
    }

    if (filters.committee.length > 0) {
      params.set('committee', filters.committee.join(','))
    }

    if (pagination.page > 1) {
      params.set('page', pagination.page.toString())
    }

    if (pagination.limit !== 12) {
      params.set('limit', pagination.limit.toString())
    }

    return params
  }, [deferredSearchQuery, filters, pagination])

  // Fetch data when parameters change
  const fetchData = async (params: URLSearchParams) => {
    setLoading(true)
    setFetchError(null)

    try {
      const baseUrl = typeof window === 'undefined'
        ? (process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000')
        : window.location.origin

      const response = await fetch(`${baseUrl}/api/members?${params.toString()}`, {
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      setData(result.data)
      setPagination(prev => ({
        ...prev,
        page: result.data.page,
        totalPages: result.data.totalPages
      }))

    } catch (err: unknown) {
      console.error('Directory fetch error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load member data'
      setFetchError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Update URL and fetch data when parameters change
  useEffect(() => {
    const paramsString = urlParams.toString()

    if (paramsString !== searchParams.toString()) {
      // Update URL without causing navigation
      const newUrl = `${window.location.pathname}${paramsString ? `?${paramsString}` : ''}`
      window.history.replaceState(null, '', newUrl)

      if (paramsString || searchParams.toString()) {
        fetchData(urlParams)
      }
    }
  }, [urlParams, searchParams])

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // Handle filter changes
  const handleFilterChange = (type: 'district' | 'committee', values: string[]) => {
    if (type === 'district') {
      setFilters(prev => ({ ...prev, district: values }))
    } else {
      setFilters(prev => ({ ...prev, committee: values }))
    }
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
  }

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  // Clear all filters
  const clearAllFilters = () => {
    setSearchQuery('')
    setFilters({ district: [], committee: [] })
    setPagination(prev => ({ ...prev, page: 1, limit: 12 }))
  }

  return (
    <div className="space-y-8">
      {/* Search and Filter Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Find Members
        </h2>

        {/* Search Input */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search by name, classification, or leadership role..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
            <svg
              className="absolute left-3 top-3.5 h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <FilterSelect
            label="Rotary District"
            options={DISTRICT_OPTIONS}
            selectedValues={filters.district}
            onChange={(values) => handleFilterChange('district', values)}
          />

          <FilterSelect
            label="Committee Membership"
            options={COMMITTEE_OPTIONS}
            selectedValues={filters.committee}
            onChange={(values) => handleFilterChange('committee', values)}
          />
        </div>

        {/* Active Filters Display */}
        {(deferredSearchQuery || filters.district.length > 0 || filters.committee.length > 0) && (
          <div className="flex flex-wrap items-center gap-2 mb-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>

            {deferredSearchQuery && (
              <FilterChip
                label={`Search: "${deferredSearchQuery}"`}
                onRemove={() => setSearchQuery('')}
              />
            )}

            {filters.district.map(district => (
              <FilterChip
                key={district}
                label={DISTRICT_OPTIONS.find(d => d.value === district)?.label || district}
                onRemove={() => handleFilterChange('district', filters.district.filter(d => d !== district))}
              />
            ))}

            {filters.committee.map(committee => (
              <FilterChip
                key={committee}
                label={COMMITTEE_OPTIONS.find(c => c.value === committee)?.label || committee}
                onRemove={() => handleFilterChange('committee', filters.committee.filter(c => c !== committee))}
              />
            ))}

            <button
              onClick={clearAllFilters}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <MemberCardSkeleton key={i} />
          ))}
        </div>
      )}

      {/* Error State */}
      {!loading && fetchError && (
        <div className="text-center py-12">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
              Error Loading Members
            </h3>
            <p className="text-red-700 dark:text-red-300 text-sm mb-4">
              {fetchError}
            </p>
            <button
              onClick={() => fetchData(urlParams)}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Results */}
      {!loading && !fetchError && data && (
        <>
          {/* Results Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {data.total === 0 ? 'No Members Found' : `Found ${data.total} Member${data.total !== 1 ? 's' : ''}`}
            </h3>

            {data.total > 0 && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <span>Showing page {data.page} of {data.totalPages}</span>
                <span>•</span>
                <span>{data.members.length} results</span>
              </div>
            )}
          </div>

          {/* Member Grid */}
          {data.members.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {data.members.map(member => (
                <MemberCard key={member.id} member={member} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No Members Match Your Criteria
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Try adjusting your search terms or filters to find more members.
              </p>
              <button
                onClick={clearAllFilters}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          )}

          {/* Pagination */}
          {data.members.length > 0 && data.totalPages > 1 && (
            <Pagination
              currentPage={data.page}
              totalPages={data.totalPages}
              onPageChange={handlePageChange}
              hasNextPage={data.hasNextPage}
              hasPrevPage={data.hasPrevPage}
            />
          )}
        </>
      )}
    </div>
  )
}

// Helper Components

interface FilterSelectProps {
  label: string
  options: Array<{ label: string, value: string }>
  selectedValues: string[]
  onChange: (values: string[]) => void
}

function FilterSelect({ label, options, selectedValues, onChange }: FilterSelectProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-left"
      >
        <span className="text-sm font-medium text-gray-700 dark:text-white">
          {label} {selectedValues.length > 0 && `(${selectedValues.length})`}
        </span>
        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {options.map(option => (
            <label key={option.value} className="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer">
              <input
                type="checkbox"
                checked={selectedValues.includes(option.value)}
                onChange={(e) => {
                  if (e.target.checked) {
                    onChange([...selectedValues, option.value])
                  } else {
                    onChange(selectedValues.filter(v => v !== option.value))
                  }
                }}
                className="mr-3 text-blue-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">{option.label}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  )
}

interface FilterChipProps {
  label: string
  onRemove: () => void
}

function FilterChip({ label, onRemove }: FilterChipProps) {
  return (
    <span className="inline-flex items-center px-3 py-1 rounded-full bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-sm">
      {label}
      <button
        onClick={onRemove}
        className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
      >
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </span>
  )
}

function MemberCardSkeleton() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
      <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full mb-4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
      </div>
    </div>
  )
}

interface MemberCardProps {
  member: Member
}

function MemberCard({ member }: MemberCardProps) {
  const primaryName = member.name?.en || member.name?.fr || member.name?.ar || 'Member'
  const primaryClassification = member.classification?.en || member.classification?.fr || member.classification?.ar || 'Professional'

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
      {/* Avatar Placeholder */}
      <div className="flex items-start space-x-4">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span className="text-white font-bold text-xl">
            {(primaryName.split(' ').map(n => n[0]).join('') || 'M').slice(0, 2).toUpperCase()}
          </span>
        </div>

        <div className="flex-1 min-w-0">
          {/* Name and Classification */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {primaryName}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {primaryClassification}
          </p>

          {/* Rotary Affiliation */}
          <div className="space-y-1 mb-4">
            <p className="text-sm text-gray-500 dark:text-gray-500">
              District {member.rotaryDistrict} • {member.rotaryClub || 'Tunis Doyen'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Member since {new Date(member.joiningDate).getFullYear()}
            </p>
          </div>

          {/* Current Leadership */}
          {member.leadershipRoles && member.leadershipRoles.filter(role => role.isCurrent).length > 0 && (
            <div className="mb-3">
              <p className="text-sm text-gray-700 dark:text-white font-medium mb-1">Current Roles:</p>
              <div className="flex flex-wrap gap-1">
                {member.leadershipRoles
                  .filter(role => role.isCurrent)
                  .slice(0, 2)
                  .map((role, index) => (
                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
                      {role.position?.en || role.position?.fr || role.position?.ar || 'Leader'}
                    </span>
                  ))}
              </div>
            </div>
          )}

          {/* Contact Information (only if consent given) */}
          {member.showContact && (member.phonePersonal || member.phoneWork) && (
            <div className="mb-3">
              <p className="text-sm text-gray-700 dark:text-white font-medium mb-1">Contact:</p>
              <div className="space-y-1">
                {member.phonePersonal && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    {member.phonePersonal}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Service Projects Summary */}
          {member.serviceProjectCount && member.serviceProjectCount > 0 && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              {member.serviceProjectCount} service project{member.serviceProjectCount !== 1 ? 's' : ''} completed
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  hasNextPage: boolean
  hasPrevPage: boolean
}

function Pagination({ currentPage, totalPages, onPageChange, hasNextPage, hasPrevPage }: PaginationProps) {
  if (totalPages <= 1) return null

  return (
    <div className="flex justify-center mt-8">
      <div className="flex space-x-2">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={!hasPrevPage}
          className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Previous
        </button>

        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          const pageNum = i + Math.max(1, currentPage - 2)
          if (pageNum > totalPages) return null

          return (
            <button
              key={pageNum}
              onClick={() => onPageChange(pageNum)}
              className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                pageNum === currentPage
                  ? 'bg-blue-600 text-white'
                  : 'bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              {pageNum}
            </button>
          )
        })}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={!hasNextPage}
          className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Next
        </button>
      </div>
    </div>
  )
}