import type { Metadata } from 'next/types'

import { Pagination } from '@/components/Pagination'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import type { Event } from '@/payload-types'

import { EventArchive } from '@/components/EventArchive'

// Export revalidation settings for good performance
export const dynamic = 'force-static'
export const revalidate = 300 // Revalidate every 5 minutes for event updates

export default async function EventsPage() {
  const payload = await getPayload({ config: configPromise })

  // Fetch events with optimized fields and pagination
  const events = await payload.find({
    collection: 'events',
    depth: 1,
    limit: 12,
    overrideAccess: false,
    pagination: true, // Enable pagination
    select: {
      id: true,
      slug: true,
      title: true,
      eventDate: true,
      location: true,
      eventType: true,
      description: true,
      capacity: true,
      attendees: true,
      status: true,
      registrationRequired: true,
    },
    // Only show published events by default
    where: {
      status: {
        equals: 'published',
      },
    },
    // Sort by event date (upcoming first)
    sort: 'eventDate',
  })

  return (
    <div className="pt-24 pb-24">
      <div className="container mb-16">
        <div className="prose dark:prose-invert max-w-none text-center">
          <h1 className="mb-4">Upcoming Events</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our upcoming Rotary events. Join us for meetings, workshops, community service
            activities, and special fundraisers that make a difference in our community.
          </p>
        </div>
      </div>

      {/* Results count */}
      <div className="container mb-8">
        <div className="flex items-center justify-between">
          <p className="text-muted-foreground">
            Showing {events.docs.length} of {events.totalDocs} events
          </p>
          <div className="text-sm text-muted-foreground">
            Page {events.page} of {events.totalPages}
          </div>
        </div>
      </div>

      {/* Event Archive with filtering */}
      <EventArchive events={events.docs as Event[]} showEventType={true} showFilters={true} />

      {/* Pagination controls */}
      <div className="container mt-16">
        {events.totalPages > 1 && events.page && (
          <Pagination page={events.page} totalPages={events.totalPages} />
        )}
      </div>
    </div>
  )
}

// Event archive search/routing metadata
export function generateMetadata(): Metadata {
  return {
    title: 'Events | Rotary Club Tunis Doyen',
    description:
      'Upcoming events and activities organized by Rotary Club Tunis Doyen. Join us for community service, meetings, workshops, and fundraisers.',
  }
}
