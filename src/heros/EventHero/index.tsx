import React from 'react'

import type { Event } from '@/payload-types'

import { formatDateTime } from '@/utilities/formatDateTime'

export const EventHero: React.FC<{
  event: Event
}> = ({ event }) => {
  const {
    title,
    eventDate,
    location,
    eventType,
    description,
    capacity,
    attendees,
    organizer,
    status,
    registrationRequired
  } = event

  const attendeeCount = attendees?.length || 0
  const isFull = capacity && attendeeCount >= capacity
  const isPast = eventDate && new Date(eventDate) < new Date()

  // Format event details
  const formattedEventDate = eventDate ? formatDateTime(eventDate) : 'TBD'
  const displayLocation = location || 'TBD'

  // Event type colors for hero
  const getEventTypeInfo = (type: string) => {
    switch (type) {
      case 'meeting': return { label: 'Meeting', bg: 'bg-blue-500', text: 'text-blue-100' }
      case 'workshop': return { label: 'Workshop', bg: 'bg-green-500', text: 'text-green-100' }
      case 'seminar': return { label: 'Seminar', bg: 'bg-purple-500', text: 'text-purple-100' }
      case 'conference': return { label: 'Conference', bg: 'bg-orange-500', text: 'text-orange-100' }
      case 'social': return { label: 'Social Event', bg: 'bg-pink-500', text: 'text-pink-100' }
      case 'fundraiser': return { label: 'Fundraiser', bg: 'bg-red-500', text: 'text-red-100' }
      case 'service': return { label: 'Community Service', bg: 'bg-teal-500', text: 'text-teal-100' }
      default: return { label: 'Event', bg: 'bg-gray-500', text: 'text-gray-100' }
    }
  }

  const eventTypeInfo = eventType ? getEventTypeInfo(eventType) : getEventTypeInfo('')

  return (
    <div className="relative -mt-[10.4rem] flex items-end">
      <div className="container z-10 relative max-w-full lg:grid lg:grid-cols-[1fr_48rem_1fr] text-white pb-8 px-4 sm:px-6">
        <div className="col-start-1 col-span-1 md:col-start-2 md:col-span-2">
          {/* Event Type Badge */}
          <div className="uppercase text-sm mb-4">
            <span className={`inline-flex items-center px-3 py-1 rounded-full ${eventTypeInfo.bg} ${eventTypeInfo.text} text-xs font-medium mb-2`}>
              {eventTypeInfo.label}
            </span>
          </div>

          {/* Event Title */}
          <div className="">
            <h1 className="mb-4 text-2xl sm:text-3xl md:text-4xl lg:text-5xl leading-tight">{title}</h1>
          </div>

          {/* Event Description Preview */}
          {description && (
            <div className="mb-6">
              <p className="text-lg opacity-90 leading-relaxed">
                {description.length > 200 ? `${description.slice(0, 200)}...` : description}
              </p>
            </div>
          )}

          {/* Status and Registration Info */}
          <div className="flex flex-wrap gap-4 mb-6">
            {/* Status Badge */}
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              status === 'published' ? 'bg-green-500 text-green-100' :
              status === 'draft' ? 'bg-yellow-500 text-yellow-100' :
              status === 'cancelled' ? 'bg-red-500 text-red-100' :
              status === 'completed' ? 'bg-blue-500 text-blue-100' : 'bg-gray-500 text-gray-100'
            }`}>
              {status === 'published' ? 'Active Event' : status?.charAt(0).toUpperCase() + status?.slice(1)}
            </span>

            {/* Registration Status */}
            {registrationRequired && capacity && (
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                isFull ? 'bg-red-500 text-red-100' : 'bg-green-500 text-green-100'
              }`}>
                {attendeeCount} / {capacity} Registered
              </span>
            )}

            {/* Past Event Indicator */}
            {isPast && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-500 text-gray-100">
                Event Ended
              </span>
            )}
          </div>

          {/* Event Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            {/* Date & Time */}
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2 mb-1">
                <svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm font-semibold">Date & Time</p>
              </div>
              <time className="opacity-90" dateTime={eventDate}>
                {formattedEventDate}
              </time>
            </div>

            {/* Location */}
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2 mb-1">
                <svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <p className="text-sm font-semibold">Location</p>
              </div>
              <p className="opacity-90">{displayLocation}</p>
            </div>

            {/* Organizer */}
            {organizer?.name && (
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2 mb-1">
                  <svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <p className="text-sm font-semibold">Organizer</p>
                </div>
                <div>
                  <p className="opacity-90">{organizer.name}</p>
                  {organizer.email && (
                    <p className="text-xs opacity-75">{organizer.email}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Registration Call to Action */}
          {!isPast && registrationRequired && (
            <div className="mt-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#registration"
                  className={`inline-flex items-center justify-center px-6 py-3 rounded-md font-medium transition-colors ${
                    isFull
                      ? 'bg-gray-600 text-gray-100 cursor-not-allowed'
                      : 'bg-white text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  {isFull ? 'Event is Full' : 'Register Now'}
                  {!isFull && (
                    <svg
                      className="ml-2 -mr-1 w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </a>
                <p className="text-xs opacity-75 self-end">
                  {capacity ? `${attendeeCount} of ${capacity} spots filled` : 'Registration open'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Hero Background */}
      <div className="min-h-[70vh] select-none">
        {/* Placeholder for event hero image - this will be expanded later */}
        <div className="w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 absolute inset-0" />
        <div className="absolute pointer-events-none left-0 bottom-0 w-full h-1/2 bg-gradient-to-t from-black to-transparent" />
      </div>
    </div>
  )
}