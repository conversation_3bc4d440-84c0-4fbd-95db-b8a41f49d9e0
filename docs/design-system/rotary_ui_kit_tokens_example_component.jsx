/*
Rotary UI Kit - Drop-in tokens JSON + Example React component (Refined for Rotary.org brand alignment)
- Default export: a single React component previewing tokens + components
- Tailwind classes are used for layout; tokens are exposed as a JS object for drop-in usage.

How to use:
1. Copy the TOKENS object into your design system (JSON or SCSS/CSS variables).
2. Use the React component as an example reference for building components in your app.
3. To convert tokens to CSS custom properties, map TOKENS to :root in your build step.

Note: this file is intentionally self-contained for demo purposes. Replace imagery, copy, and icon SVGs with production assets.
*/

// ---- TOKENS (drop-in JSON-like object) ----
export const TOKENS = {
  "color": {
    "brand": {
      "royalBlue": "#17458F",
      "royalBlueDark": "#123F7A",
      "gold": "#F7A81B"
    },
    "status": {
      "success": "#009739",
      "error": "#D41367",
      "warning": "#FF7600",
      "info": "#00A2E0"
    },
    "neutral": {
      "white": "#FFFFFF",
      "nearBlack": "#111111",
      "trueBlack": "#000000",
      "charcoal": "#54565A",
      "slate": "#657F99",
      "smoke": "#B1B1B1",
      "powderBlue": "#B9D9EB"
    },
    "surface": {
      "overlay": "rgba(23,69,143,0.6)"
    }
  },
  "typography": {
    "family": {
      "primary": "\"Open Sans\", Arial, system-ui, sans-serif"
    },
    "link": {
      "hoverColor": "rgb(18,63,122)",
      "visitedStyle": "inherit"
    },
    "scale": {
      "h1": "56px",
      "h2": "40px",
      "h3": "28px",
      "body": "16px",
      "small": "13px"
    },
    "weights": {
      "regular": 400,
      "medium": 500,
      "semibold": 600,
      "bold": 700
    }
  },
  "spacing": {
    "base": 8,
    "scale": [0,4,8,12,16,24,32,40,48,64]
  },
  "radius": {
    "sm": "6px",
    "md": "8px",
    "lg": "12px",
    "pill": "999px"
  },
  "shadow": {
    "sm": "0 1px 2px rgba(16,24,40,0.05)",
    "md": "0 6px 18px rgba(16,24,40,0.08)"
  },
  "grid": {
    "columns": 12,
    "containerMax": "1200px"
  }
};

// Optional: export JSON string for systems that expect a .json file.
export const TOKENS_JSON = JSON.stringify(TOKENS, null, 2);

// ---- Example helper: map tokens to CSS variables (run on app init) ----
export function injectCssVariables(prefix = '--ri') {
  if (typeof document === 'undefined') return;
  const root = document.documentElement;
  const set = (k, v) => root.style.setProperty(`${prefix}-${k}`, v);

  // colors
  Object.entries(TOKENS.color.brand).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.status).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.neutral).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.surface).forEach(([k,v]) => set(`color-${k}`, v));
  // typography
  Object.entries(TOKENS.typography.link).forEach(([k,v]) => set(`typo-link-${k}`, v));
  // radius
  Object.entries(TOKENS.radius).forEach(([k,v]) => set(`radius-${k}`, v));
  // shadows
  Object.entries(TOKENS.shadow).forEach(([k,v]) => set(`shadow-${k}`, v.replace(/"/g,'\\"')));
}

// ---- Example React component ----
import React, { useEffect, useState } from 'react';

export default function RotaryDemoComponent() {
  useEffect(() => { injectCssVariables(); }, []);

  const [lang, setLang] = useState('fr');
  const [dir, setDir] = useState('ltr');

  useEffect(() => {
    setDir(lang === 'ar' ? 'rtl' : 'ltr');
  }, [lang]);

  const stats = [
    { number: '1.2 million', label: 'members worldwide' },
    { number: '47 million', label: 'volunteer hours' },
    { number: '$291 million', label: 'for sustainable projects' }
  ];

  const events = [
    { title: 'Community Cleanup', location: 'Tunis', date: 'Sept 14, 2025' },
    { title: 'Youth Leadership Camp', location: 'Sousse', date: 'Oct 20, 2025' },
    { title: 'Health & Wellness Fair', location: 'Sfax', date: 'Nov 5, 2025' }
  ];

  return (
    <div dir={dir} className="min-h-screen bg-gray-50 text-neutral-800" style={{fontFamily: TOKENS.typography.family.primary}}>
      {/* Top nav */}
      <header className="w-full shadow-sm bg-white fixed top-0 left-0 right-0 z-10" style={{boxShadow: TOKENS.shadow.sm}}>
        <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full flex items-center justify-center bg-[var(--ri-color-royalBlue)] text-white font-bold text-sm">RD</div>
            <div className="hidden sm:block">
              <div className="text-sm font-semibold text-[var(--ri-color-royalBlue)] leading-tight">Rotary Club</div>
              <div className="text-xs text-[var(--ri-color-slate)] leading-tight">Tunis Doyen</div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <nav className="hidden md:flex gap-6 text-sm font-medium" style={{color: TOKENS.color.neutral.charcoal}}>
              <a className="hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" href="#">About</a>
              <a className="hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" href="#">Our Causes</a>
              <a className="hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" href="#">News</a>
              <a className="hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" href="#">Get Involved</a>
            </nav>

            <select aria-label="Language" value={lang} onChange={(e)=>setLang(e.target.value)} className="border rounded-md px-2 py-1 text-sm bg-gray-50 border-gray-300 focus:outline-none focus:ring-2 focus:ring-[var(--ri-color-royalBlue)]">
              <option value="en">EN</option>
              <option value="fr">FR</option>
              <option value="ar">AR</option>
            </select>
            <a href="#" className="hidden sm:inline-flex items-center px-4 py-2 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-[var(--ri-color-royalBlueDark)]" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>Donate</a>
          </div>
        </div>
      </header>
      <div className="h-16"></div>

      {/* Hero */}
      <main>
        <section className="relative w-full h-[50vh] md:h-[65vh] flex items-center justify-center text-center px-4" style={{backgroundImage: "url(https://images.unsplash.com/photo-1524504388940-b1c1722653e1?q=80&w=1600&auto=format&fit=crop)", backgroundSize: 'cover', backgroundPosition: 'center'}}>
          <div className="absolute inset-0" style={{backgroundColor: TOKENS.color.surface.overlay}}></div>
          <div className="relative text-white max-w-2xl">
            <h1 className="text-[36px] sm:text-[48px] md:text-[56px] font-bold leading-tight drop-shadow-md">
              We are <span className="text-[var(--ri-color-gold)]">People of Action</span>
            </h1>
            <p className="mt-4 text-sm sm:text-base font-medium drop-shadow-sm">A worldwide network of professional and community leaders helping to find solutions for the world’s most pressing challenges.</p>
            <a href="#" className="inline-flex items-center mt-8 px-8 py-3 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-[var(--ri-color-royalBlueDark)]" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>
              Take Action With Us
            </a>
          </div>
        </section>

        {/* Stats Section */}
        <section className="bg-white py-12 px-4">
          <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-3 gap-8">
            {stats.map((s, i) => (
              <div key={i} className="text-center">
                <div className="text-[40px] font-bold" style={{color: TOKENS.color.brand.royalBlue}}>{s.number}</div>
                <div className="text-lg font-medium mt-1" style={{color: TOKENS.color.neutral.nearBlack}}>{s.label}</div>
              </div>
            ))}
          </div>
        </section>

        {/* What We Do Section */}
        <section className="py-16 px-4 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center" style={{color: TOKENS.color.brand.royalBlue}}>What We Do</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              <div className="bg-white rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold" style={{color: TOKENS.color.neutral.nearBlack}}>Community Projects</h3>
                <p className="mt-2 text-[var(--ri-color-charcoal)]">We lead and participate in local service projects that have a lasting impact in our communities.</p>
              </div>
              <div className="bg-white rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold" style={{color: TOKENS.color.neutral.nearBlack}}>Global Initiatives</h3>
                <p className="mt-2 text-[var(--ri-color-charcoal)]">Rotary is a global network of leaders and we are part of initiatives that tackle the world's biggest challenges.</p>
              </div>
              <div className="bg-white rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold" style={{color: TOKENS.color.neutral.nearBlack}}>Youth Programs</h3>
                <p className="mt-2 text-[var(--ri-color-charcoal)]">We support future generations with youth leadership development and exchange programs.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Events Section */}
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center" style={{color: TOKENS.color.brand.royalBlue}}>Upcoming Events</h2>
            <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {events.map((event, i) => (
                <article key={i} className="flex flex-col rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="p-6 bg-white flex-grow">
                    <h3 className="text-xl font-semibold" style={{color: TOKENS.color.neutral.nearBlack}}>{event.title}</h3>
                    <p className="mt-1 text-sm font-medium" style={{color: TOKENS.color.neutral.charcoal}}>{event.location}</p>
                    <time className="mt-1 text-sm font-medium block" style={{color: TOKENS.color.neutral.slate}}>{event.date}</time>
                  </div>
                  <div className="px-6 py-4 border-t border-gray-200">
                    <a href="#" className="inline-flex items-center px-6 py-3 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-[var(--ri-color-royalBlueDark)]" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>
                      Register Now
                    </a>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>

      </main>

      <footer className="border-t py-8 mt-16 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm" style={{color: TOKENS.color.neutral.charcoal}}>© {new Date().getFullYear()} Rotary Club Tunis Doyen — All rights reserved</div>
          <div className="flex gap-4">
            <a href="#" className="text-sm hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" style={{color: TOKENS.color.neutral.charcoal}}>Privacy Policy</a>
            <a href="#" className="text-sm hover:text-[var(--ri-color-royalBlue)] transition-colors duration-200" style={{color: TOKENS.color.neutral.charcoal}}>Contact Us</a>
          </div>
        </div>
      </footer>
    </div>
  );
}
