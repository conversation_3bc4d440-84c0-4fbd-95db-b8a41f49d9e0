import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import { resolve } from 'path'

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    include: [
      'src/app/**/*.test.{ts,tsx}',
      'src/utilities/__tests__/**/*.test.ts',
      'tests/int/**/*.int.spec.ts',
      'tests/**/*.test.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      all: false,
      include: [
        'src/utilities/*.ts',
        'src/utilities/*.tsx'
      ],
      exclude: [
        'node_modules/**',
        'src/**/*.test.*',
        'src/**/*/__tests__/**',
        'src/components/**',
        'src/app/**',
        'src/collections/**',
        'src/payload-types.ts',
        'tests/**/*.config.*',
        'tests/fixtures/**',
        'tests/mocks/**',
        'tests/helpers/**'
      ],
      reportsDirectory: './coverage/utilities',
      thresholds: {
        global: {
          statements: 75,
          branches: 70,
          functions: 80,
          lines: 75
        },
        // Specific thresholds for utility functions
        'src/utilities/canUseDOM.ts': {
          statements: 100,
          functions: 100,
          lines: 100
        }
      }
    },
    // GDPR test specific configuration
    globals: true,
    deps: {
      // Ensure proper import handling for Payload CMS and GDPR modules
      interopDefault: true,
      moduleDirectories: ['node_modules']
    },
  },
})
