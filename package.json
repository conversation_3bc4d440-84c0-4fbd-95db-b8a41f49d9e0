{"name": "rotary-cms", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "type-check": "tsc --noEmit", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "dotenv -e test.env -- vitest run", "test:csv": "vitest run '**/eventExports.test.ts'", "test:performance": "vitest run '**/performance.test.ts'", "test:all:csv": "pnpm run test && pnpm run test:e2e", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:csv": "playwright test csv-export-browser.spec.ts", "test:gdpr": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/", "test:gdpr:coverage": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/ --coverage", "test:gdpr:html": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/ --coverage --reporter=html", "test:accessibility": "vitest run tests/accessibility/", "test:accessibility:coverage": "vitest run tests/accessibility/ --coverage --coverage.reportsDirectory='./coverage/accessibility'", "test:i18n": "vitest run tests/unit/i18n-validation.test.ts tests/e2e/i18n-*.spec.ts tests/int/i18n-*.spec.ts", "test:i18n:coverage": "vitest run tests/unit/i18n-validation.test.ts tests/e2e/i18n-*.spec.ts tests/int/i18n-*.spec.ts --coverage --coverage.reportsDirectory='./coverage/i18n'", "test:gdpr:simple": "dotenv -e test.env -- vitest run tests/gdpr/gdpr-compliance-simple.test.ts --coverage --coverage.reportsDirectory='./coverage/gdpr-simple'", "test:gdpr:all": "dotenv -e test.env -- vitest run tests/gdpr/ --config '@/vitest.gdpr.config.mts'", "test:coverage:all": "dotenv -e test.env -- vitest run --config=./vitest.comprehensive.config.mts --coverage", "test:coverage:comprehensive": "dotenv -e test.env -- vitest run --config=./vitest.comprehensive.config.mts --coverage --reporter=html", "test:coverage:unified": "pnpm run test:coverage:comprehensive && pnpm run test:gdpr:simple && pnpm run test:accessibility:coverage", "test:coverage:failed": "dotenv -e test.env -- vitest run --bail=3 --coverage --coverage.reportOnFailure=true", "test:coverage:watch": "pnpm run test:coverage:all -- --watch", "quality:monitor": "node scripts/quality-monitoring.js", "quality:export": "node scripts/quality-monitoring.js --export", "quality:dashboard": "open coverage/comprehensive/index.html", "deploy:check": "pnpm run quality:monitor && echo '✅ Quality gates passed - Ready for deployment'"}, "dependencies": {"@fontsource/tajawal": "^5.2.6", "@payloadcms/admin-bar": "3.53.0", "@payloadcms/db-mongodb": "3.53.0", "@payloadcms/email-resend": "3.53.0", "@payloadcms/live-preview-react": "3.53.0", "@payloadcms/next": "3.53.0", "@payloadcms/payload-cloud": "3.53.0", "@payloadcms/plugin-form-builder": "3.53.0", "@payloadcms/plugin-nested-docs": "3.53.0", "@payloadcms/plugin-redirects": "3.53.0", "@payloadcms/plugin-search": "3.53.0", "@payloadcms/plugin-seo": "3.53.0", "@payloadcms/richtext-lexical": "3.53.0", "@payloadcms/translations": "^3.53.0", "@payloadcms/ui": "3.53.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "dotenv": "16.4.7", "eslint-plugin-react-hooks": "^5.2.0", "geist": "^1.3.0", "graphql": "^16.8.2", "lucide-react": "^0.378.0", "next": "15.4.4", "next-sitemap": "^4.2.3", "nodemailer": "^7.0.5", "payload": "3.53.0", "prism-react-renderer": "^2.3.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.45.4", "resend": "^6.0.1", "sanitize-html": "^2.17.0", "sharp": "0.34.2", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.54.1", "@tailwindcss/typography": "^0.5.13", "@testing-library/react": "16.3.0", "@types/escape-html": "^1.0.2", "@types/node": "22.5.4", "@types/nodemailer": "^7.0.1", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/sanitize-html": "^2.16.0", "@vitejs/plugin-react": "4.5.2", "@vitest/coverage-v8": "1.6.0", "autoprefixer": "^10.4.19", "copyfiles": "^2.4.1", "dotenv-cli": "^10.0.0", "eslint": "^9.16.0", "eslint-config-next": "15.4.4", "jsdom": "26.1.0", "playwright": "1.54.1", "playwright-core": "1.54.1", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.3", "tailwindcss-rtl": "^0.9.0", "typescript": "5.7.3", "vite-tsconfig-paths": "5.1.4", "vitest": "1.6.0"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}