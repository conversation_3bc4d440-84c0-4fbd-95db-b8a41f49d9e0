import { describe, it, expect, vi, beforeEach } from 'vitest'
import { sendEmail, getRegistrationConfirmationTemplate, getOrganizerNotificationTemplate, closeTransporter, EmailSendError } from '../src/utilities/emailService'
import nodemailer from 'nodemailer'
import type { Payload } from 'payload'

// Mock nodemailer
vi.mock('nodemailer', () => ({
  default: {
    createTransport: vi.fn(),
  },
}))

// Mock Payload
const mockPayload = {
  find: vi.fn(),
  logger: {
    error: vi.fn(),
  },
} as unknown as Payload

describe('Email Service', () => {
  let mockTransport: {
    sendMail: vi.Mock
    on: vi.Mock
    close: vi.Mock
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockTransport = {
      sendMail: vi.fn().mockResolvedValue({ messageId: 'test-message-id' }),
      on: vi.fn(),
      close: vi.fn(),
    }
    ;(nodemailer.createTransport as vi.Mock).mockReturnValue(mockTransport)
    
    // Reset mock implementation
    mockPayload.find = vi.fn()
  })

  describe('getRegistrationConfirmationTemplate', () => {
    it('should generate English template', async () => {
      // Mock to return null so it falls back to hardcoded templates
      (mockPayload.find as vi.Mock).mockResolvedValue({ docs: [] })
      
      const template = await getRegistrationConfirmationTemplate(
        mockPayload,
        'Test Event',
        'Friday, January 15, 2025 at 2:00 PM',
        'Rotary Club Tunis Doyen',
        'John Doe',
        'en',
      )

      expect(template.subject).toContain('Test Event')
      expect(template.html).toContain('John Doe')
      expect(template.html).toContain('Friday, January 15, 2025')
      expect(template.text).toContain('Test Event')
    })

    it('should generate French template', async () => {
      // Mock to return null so it falls back to hardcoded templates
      (mockPayload.find as vi.Mock).mockResolvedValue({ docs: [] })
      
      const template = await getRegistrationConfirmationTemplate(
        mockPayload,
        'Test Event',
        'Friday, January 15, 2025 at 2:00 PM',
        'Rotary Club Tunis Doyen',
        'Jean Dupont',
        'fr',
      )

      expect(template.subject).toContain('Test Event')
      expect(template.html).toContain('Jean Dupont')
      expect(template.html).toContain('Inscription Confirmée')
      expect(template.text).toContain('Test Event')
    })

    it('should generate Arabic template', async () => {
      // Mock to return null so it falls back to hardcoded templates
      (mockPayload.find as vi.Mock).mockResolvedValue({ docs: [] })
      
      const template = await getRegistrationConfirmationTemplate(
        mockPayload,
        'Test Event',
        'Friday, January 15, 2025 at 2:00 PM',
        'Rotary Club Tunis Doyen',
        'أحمد محمد',
        'ar',
      )

      expect(template.subject).toContain('Test Event')
      expect(template.html).toContain('Test Event')
      expect(template.html).toContain('تم تأكيد التسجيل')
      expect(template.text).toContain('Test Event')
    })
  })

  describe('getOrganizerNotificationTemplate', () => {
    it('should generate English organizer notification', async () => {
      // Mock to return null so it falls back to hardcoded templates
      (mockPayload.find as vi.Mock).mockResolvedValue({ docs: [] })
      
      const template = await getOrganizerNotificationTemplate(
        mockPayload,
        'Test Event',
        'Friday, January 15, 2025 at 2:00 PM',
        'Rotary Club Tunis Doyen',
        'John Doe',
        '<EMAIL>',
        'January 15, 2025',
        'en',
      )

      expect(template.subject).toContain('New Registration')
      expect(template.html).toContain('John Doe')
      expect(template.html).toContain('<EMAIL>')
      expect(template.text).toContain('Test Event')
    })

    it('should generate French organizer notification', async () => {
      // Mock to return null so it falls back to hardcoded templates
      (mockPayload.find as vi.Mock).mockResolvedValue({ docs: [] })
      
      const template = await getOrganizerNotificationTemplate(
        mockPayload,
        'Test Event',
        'Friday, January 15, 2025 at 2:00 PM',
        'Rotary Club Tunis Doyen',
        'Jean Dupont',
        '<EMAIL>',
        'January 15, 2025',
        'fr',
      )

      expect(template.subject).toContain('Nouvelle Inscription')
      expect(template.html).toContain('Jean Dupont')
      expect(template.html).toContain('<EMAIL>')
      expect(template.text).toContain('Test Event')
    })
  })

  describe('sendEmail', () => {
    it('should send email successfully', async () => {
      await closeTransporter() // Reset transporter before test
      const result = await sendEmail({
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
        text: 'Test Text',
      })

      expect(result).toBe(true)
      expect(nodemailer.createTransport).toHaveBeenCalledTimes(1)
      expect(mockTransport.sendMail).toHaveBeenCalledWith({
        from: '"Rotary Club Tunis Doyen" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
        text: 'Test Text',
      })
    })

    it('should handle email sending errors', async () => {
      await closeTransporter() // Reset transporter before test
      mockTransport.sendMail.mockRejectedValue(new Error('SMTP Error'))

      await expect(
        sendEmail({
          to: '<EMAIL>',
          subject: 'Test Subject',
          html: '<p>Test HTML</p>',
          text: 'Test Text',
        }),
      ).rejects.toThrow(EmailSendError)
    })
  })
})