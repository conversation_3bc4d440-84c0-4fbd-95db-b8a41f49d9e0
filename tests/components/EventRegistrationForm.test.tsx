import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { EventRegistrationForm } from '../src/components/EventRegistrationForm'
import * as apiClient from '../src/utilities/apiClient'

// Mock the API client
jest.mock('../src/utilities/apiClient', () => ({
  get: jest.fn(),
  post: jest.fn()
}))

// Mock the sub-components
jest.mock('../src/components/EventRegistrationForm/components/BasicInfoSection', () => ({
  BasicInfoSection: () => <div data-testid="basic-info-section">Basic Info Section</div>
}))

jest.mock('../src/components/EventRegistrationForm/components/ConsentSection', () => ({
  ConsentSection: () => <div data-testid="consent-section">Consent Section</div>
}))

jest.mock('../src/components/EventRegistrationForm/components/EventStatusDisplay', () => ({
  EventStatusDisplay: () => <div data-testid="event-status-display">Event Status Display</div>
}))

jest.mock('../src/components/EventRegistrationForm/components/ProfileLoadingDisplay', () => ({
  ProfileLoadingDisplay: () => <div data-testid="profile-loading-display">Profile Loading Display</div>
}))

jest.mock('../src/components/EventRegistrationForm/components/RegistrationSuccessDisplay', () => ({
  RegistrationSuccessDisplay: () => <div data-testid="registration-success-display">Registration Success Display</div>
}))

// Mock event data
const mockEvent = {
  id: 'event-1',
  title: 'Test Event',
  capacity: 50,
  attendees: [],
  registrationRequired: true,
  status: 'published'
}

describe('EventRegistrationForm', () => {
  const mockOnRegistrationSuccess = jest.fn()
  const mockOnRegistrationError = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the form correctly', async () => {
    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1',
          name: { en: 'John Doe' },
          email: '<EMAIL>'
        }
      }
    })

    render(
      <EventRegistrationForm
        event={mockEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByTestId('basic-info-section')).toBeInTheDocument()
    })

    expect(screen.getByTestId('consent-section')).toBeInTheDocument()
    expect(screen.getByTestId('event-status-display')).toBeInTheDocument()
    expect(screen.getByTestId('profile-loading-display')).toBeInTheDocument()
  })

  it('loads member profile and pre-populates form', async () => {
    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1',
          name: { en: 'John Doe' },
          email: '<EMAIL>',
          phonePersonal: '+1234567890',
          classification: { en: 'Engineer' },
          rotaryId: '12345',
          privacySettings: {
            marketingConsent: true
          }
        }
      }
    })

    render(
      <EventRegistrationForm
        event={mockEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for profile to load
    await waitFor(() => {
      expect(apiClient.get).toHaveBeenCalledWith('/api/users/profile')
    })
  })

  it('handles profile loading errors gracefully', async () => {
    (apiClient.get as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Unauthorized',
      code: 'UNAUTHORIZED'
    })

    render(
      <EventRegistrationForm
        event={mockEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for error handling
    await waitFor(() => {
      expect(screen.getByTestId('profile-loading-display')).toBeInTheDocument()
    })
  })

  it('validates required fields', async () => {
    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1'
        }
      }
    })

    render(
      <EventRegistrationForm
        event={mockEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('basic-info-section')).toBeInTheDocument()
    })

    // Try to submit empty form
    const submitButton = screen.getByRole('button', { name: /Register for Event/i })
    fireEvent.click(submitButton)

    // Should call error handler
    await waitFor(() => {
      expect(mockOnRegistrationError).toHaveBeenCalled()
    })
  })

  it('prevents duplicate registrations', async () => {
    const eventWithAttendee = {
      ...mockEvent,
      attendees: [
        {
          userEmail: '<EMAIL>'
        }
      ]
    }

    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1',
          email: '<EMAIL>'
        }
      }
    })

    render(
      <EventRegistrationForm
        event={eventWithAttendee as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('basic-info-section')).toBeInTheDocument()
    })
  })

  it('respects event capacity limits', async () => {
    const fullEvent = {
      ...mockEvent,
      capacity: 10,
      attendees: Array(10).fill({}).map((_, i) => ({
        id: `attendee-${i}`
      }))
    }

    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1'
        }
      }
    })

    render(
      <EventRegistrationForm
        event={fullEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('event-status-display')).toBeInTheDocument()
    })

    // Submit button should be disabled
    const submitButton = screen.getByRole('button', { name: /Event Full/i })
    expect(submitButton).toBeDisabled()
  })

  it('submits registration successfully', async () => {
    (apiClient.get as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        user: {
          id: 'user-1',
          name: { en: 'John Doe' },
          email: '<EMAIL>'
        }
      }
    })

    (apiClient.post as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        registrationId: 'reg-123'
      }
    })

    render(
      <EventRegistrationForm
        event={mockEvent as any}
        onRegistrationSuccess={mockOnRegistrationSuccess}
        onRegistrationError={mockOnRegistrationError}
      />
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('basic-info-section')).toBeInTheDocument()
    })

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Register for Event/i })
    fireEvent.click(submitButton)

    // Should call success handler
    await waitFor(() => {
      expect(mockOnRegistrationSuccess).toHaveBeenCalled()
    })
  })
})