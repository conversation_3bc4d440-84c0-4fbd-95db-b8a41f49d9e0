
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for heros/EventHero/index.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">heros/EventHero</a> index.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/192</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/192</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React from 'react'</span></span></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >import type { Event } from '@/payload-types'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >import { formatDateTime } from '@/utilities/formatDateTime'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export const EventHero: React.FC&lt;{</span>
<span class="cstat-no" title="statement not covered" >  event: Event</span>
<span class="cstat-no" title="statement not covered" >}&gt; = ({ event }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const {</span>
<span class="cstat-no" title="statement not covered" >    title,</span>
<span class="cstat-no" title="statement not covered" >    eventDate,</span>
<span class="cstat-no" title="statement not covered" >    location,</span>
<span class="cstat-no" title="statement not covered" >    eventType,</span>
<span class="cstat-no" title="statement not covered" >    description,</span>
<span class="cstat-no" title="statement not covered" >    capacity,</span>
<span class="cstat-no" title="statement not covered" >    attendees,</span>
<span class="cstat-no" title="statement not covered" >    organizer,</span>
<span class="cstat-no" title="statement not covered" >    status,</span>
<span class="cstat-no" title="statement not covered" >    registrationRequired</span>
<span class="cstat-no" title="statement not covered" >  } = event</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const attendeeCount = attendees?.length || 0</span>
<span class="cstat-no" title="statement not covered" >  const isFull = capacity &amp;&amp; attendeeCount &gt;= capacity</span>
<span class="cstat-no" title="statement not covered" >  const isPast = eventDate &amp;&amp; new Date(eventDate) &lt; new Date()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Format event details</span>
<span class="cstat-no" title="statement not covered" >  const formattedEventDate = eventDate ? formatDateTime(eventDate) : 'TBD'</span>
<span class="cstat-no" title="statement not covered" >  const displayLocation = location || 'TBD'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Event type colors for hero</span>
<span class="cstat-no" title="statement not covered" >  const getEventTypeInfo = (type: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    switch (type) {</span>
<span class="cstat-no" title="statement not covered" >      case 'meeting': return { label: 'Meeting', bg: 'bg-blue-500', text: 'text-blue-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'workshop': return { label: 'Workshop', bg: 'bg-green-500', text: 'text-green-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'seminar': return { label: 'Seminar', bg: 'bg-purple-500', text: 'text-purple-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'conference': return { label: 'Conference', bg: 'bg-orange-500', text: 'text-orange-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'social': return { label: 'Social Event', bg: 'bg-pink-500', text: 'text-pink-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'fundraiser': return { label: 'Fundraiser', bg: 'bg-red-500', text: 'text-red-100' }</span>
<span class="cstat-no" title="statement not covered" >      case 'service': return { label: 'Community Service', bg: 'bg-teal-500', text: 'text-teal-100' }</span>
<span class="cstat-no" title="statement not covered" >      default: return { label: 'Event', bg: 'bg-gray-500', text: 'text-gray-100' }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const eventTypeInfo = eventType ? getEventTypeInfo(eventType) : getEventTypeInfo('')</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="relative -mt-[10.4rem] flex items-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="container z-10 relative max-w-full lg:grid lg:grid-cols-[1fr_48rem_1fr] text-white pb-8 px-4 sm:px-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="col-start-1 col-span-1 md:col-start-2 md:col-span-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* Event Type Badge */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="uppercase text-sm mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className={`inline-flex items-center px-3 py-1 rounded-full ${eventTypeInfo.bg} ${eventTypeInfo.text} text-xs font-medium mb-2`}&gt;</span>
<span class="cstat-no" title="statement not covered" >              {eventTypeInfo.label}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Event Title */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className=""&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h1 className="mb-4 text-2xl sm:text-3xl md:text-4xl lg:text-5xl leading-tight"&gt;{title}&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Event Description Preview */}</span>
<span class="cstat-no" title="statement not covered" >          {description &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="text-lg opacity-90 leading-relaxed"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {description.length &gt; 200 ? `${description.slice(0, 200)}...` : description}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Status and Registration Info */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex flex-wrap gap-4 mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {/* Status Badge */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${</span>
<span class="cstat-no" title="statement not covered" >              status === 'published' ? 'bg-green-500 text-green-100' :</span>
<span class="cstat-no" title="statement not covered" >              status === 'draft' ? 'bg-yellow-500 text-yellow-100' :</span>
<span class="cstat-no" title="statement not covered" >              status === 'cancelled' ? 'bg-red-500 text-red-100' :</span>
<span class="cstat-no" title="statement not covered" >              status === 'completed' ? 'bg-blue-500 text-blue-100' : 'bg-gray-500 text-gray-100'</span>
<span class="cstat-no" title="statement not covered" >            }`}&gt;</span>
<span class="cstat-no" title="statement not covered" >              {status === 'published' ? 'Active Event' : status?.charAt(0).toUpperCase() + status?.slice(1)}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            {/* Registration Status */}</span>
<span class="cstat-no" title="statement not covered" >            {registrationRequired &amp;&amp; capacity &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${</span>
<span class="cstat-no" title="statement not covered" >                isFull ? 'bg-red-500 text-red-100' : 'bg-green-500 text-green-100'</span>
<span class="cstat-no" title="statement not covered" >              }`}&gt;</span>
<span class="cstat-no" title="statement not covered" >                {attendeeCount} / {capacity} Registered</span>
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            {/* Past Event Indicator */}</span>
<span class="cstat-no" title="statement not covered" >            {isPast &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-500 text-gray-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >                Event Ended</span>
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Event Details Grid */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {/* Date &amp; Time */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex flex-col gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-2 mb-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm font-semibold"&gt;Date &amp; Time&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;time className="opacity-90" dateTime={eventDate}&gt;</span>
<span class="cstat-no" title="statement not covered" >                {formattedEventDate}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/time&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            {/* Location */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex flex-col gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-2 mb-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm font-semibold"&gt;Location&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="opacity-90"&gt;{displayLocation}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            {/* Organizer */}</span>
<span class="cstat-no" title="statement not covered" >            {organizer?.name &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex flex-col gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-center gap-2 mb-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;svg className="w-5 h-5 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="text-sm font-semibold"&gt;Organizer&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="opacity-90"&gt;{organizer.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {organizer.email &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-xs opacity-75"&gt;{organizer.email}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  )}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Registration Call to Action */}</span>
<span class="cstat-no" title="statement not covered" >          {!isPast &amp;&amp; registrationRequired &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="mt-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex flex-col sm:flex-row gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a</span>
<span class="cstat-no" title="statement not covered" >                  href="#registration"</span>
<span class="cstat-no" title="statement not covered" >                  className={`inline-flex items-center justify-center px-6 py-3 rounded-md font-medium transition-colors ${</span>
<span class="cstat-no" title="statement not covered" >                    isFull</span>
<span class="cstat-no" title="statement not covered" >                      ? 'bg-gray-600 text-gray-100 cursor-not-allowed'</span>
<span class="cstat-no" title="statement not covered" >                      : 'bg-white text-gray-900 hover:bg-gray-100'</span>
<span class="cstat-no" title="statement not covered" >                  }`}</span>
<span class="cstat-no" title="statement not covered" >                &gt;</span>
<span class="cstat-no" title="statement not covered" >                  {isFull ? 'Event is Full' : 'Register Now'}</span>
<span class="cstat-no" title="statement not covered" >                  {!isFull &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                      className="ml-2 -mr-1 w-4 h-4"</span>
<span class="cstat-no" title="statement not covered" >                      fill="currentColor"</span>
<span class="cstat-no" title="statement not covered" >                      viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                      xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                    &gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path</span>
<span class="cstat-no" title="statement not covered" >                        fillRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                        d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"</span>
<span class="cstat-no" title="statement not covered" >                        clipRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                      /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                  )}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs opacity-75 self-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {capacity ? `${attendeeCount} of ${capacity} spots filled` : 'Registration open'}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Hero Background */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="min-h-[70vh] select-none"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {/* Placeholder for event hero image - this will be expanded later */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 absolute inset-0" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute pointer-events-none left-0 bottom-0 w-full h-1/2 bg-gradient-to-t from-black to-transparent" /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-30T13:10:11.995Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    